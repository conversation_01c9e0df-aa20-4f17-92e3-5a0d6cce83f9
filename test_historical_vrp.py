#!/usr/bin/env python3
"""
Test script for historical VRP functionality
Verifies that 20-day VRP comparison works correctly.
"""

import sys
from constants import TEST_SYMBOL_SPX, TEST_PRICE_SPX, TEST_MONTH, TEST_YEAR, TEST_HISTORICAL_DAYS
from market_data import MarketData
from pricing_engine import PricingEngine
from vrp_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_historical_vrp():
    """Test historical VRP calculation."""
    print("🧪 Testing Historical VRP Functionality...")
    
    try:
        # Initialize components
        market_data = MarketData()
        pricing_engine = PricingEngine()
        vrp_analyzer = VRPAnalyzer(market_data, pricing_engine)
        
        # Test historical VRP calculation
        print("📊 Testing historical VRP calculation...")
        hist_data = vrp_analyzer.calculate_historical_vrp(TEST_SYMBOL_SPX, TEST_PRICE_SPX, TEST_MONTH, TEST_YEAR, days=TEST_HISTORICAL_DAYS)
        
        # Verify structure
        required_keys = [
            'historical_vrp_30d', 'avg_vrp_30d', 'current_vs_avg', 
            'vrp_trend', 'days_analyzed'
        ]
        
        for key in required_keys:
            assert key in hist_data, f"Missing key: {key}"
        
        print(f"✅ Historical VRP structure valid")
        print(f"   Days analyzed: {hist_data['days_analyzed']}")
        print(f"   Average VRP: {hist_data['avg_vrp_30d']:+.1%}")
        print(f"   VRP trend: {hist_data['vrp_trend']}")
        
        # Test full VRP analysis with historical data
        print("📊 Testing full VRP analysis with historical data...")
        vrp_signal = vrp_analyzer.analyze_vrp(TEST_SYMBOL_SPX, TEST_PRICE_SPX, TEST_MONTH, TEST_YEAR, silent=True)
        
        # Verify historical data is included
        assert vrp_signal.historical_vrp_data is not None, "Historical VRP data should be included"
        assert 'days_analyzed' in vrp_signal.historical_vrp_data, "Historical data should have days_analyzed"
        
        print(f"✅ Full VRP analysis with historical data works")
        print(f"   Signal: {vrp_signal.signal_strength}")
        print(f"   Historical days: {vrp_signal.historical_vrp_data['days_analyzed']}")
        
        # Test VRP display (should include historical comparison)
        print("📊 Testing VRP display with historical data...")
        vrp_analyzer.display_vrp_analysis(vrp_signal)
        
        return True
        
    except Exception as e:
        print(f"❌ Historical VRP test failed: {e}")
        return False


def main():
    """Run historical VRP tests."""
    print("🚀 Starting Historical VRP Tests")
    print("=" * 50)
    
    success = test_historical_vrp()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 All historical VRP tests passed!")
        return 0
    else:
        print("💡 Historical VRP tests failed - check implementation")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 