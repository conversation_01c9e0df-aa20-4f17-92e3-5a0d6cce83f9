#!/usr/bin/env python3
"""
Test script to debug Polygon API historical data calls
"""

import requests
from datetime import date, timedelta
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API key from environment
API_KEY = os.getenv('POLYGON_API_KEY')

def test_historical_api():
    """Test the historical data API call"""
    
    # Calculate dates
    end_date = date.today()
    start_date = end_date - timedelta(days=30)  # Just 30 days for testing
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    # Test with SPX (index)
    symbol = "SPX"
    api_symbol = f"I:{symbol.upper()}"
    url = f"https://api.polygon.io/v2/aggs/ticker/{api_symbol}/range/1/day/{start_str}/{end_str}"
    
    params = {
        'adjusted': 'true',
        'sort': 'asc',
        'limit': 50000,
        'apikey': API_KEY
    }
    
    print(f"Testing API call for {symbol}")
    print(f"URL: {url}")
    print(f"Params: {params}")
    print(f"Date range: {start_str} to {end_str}")
    print("-" * 60)
    
    try:
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response Status: {data.get('status')}")
            print(f"Results Count: {len(data.get('results', []))}")
            
            if data.get('results'):
                print(f"First result: {data['results'][0]}")
                print(f"Last result: {data['results'][-1]}")
            else:
                print("No results found")
                print(f"Full response: {json.dumps(data, indent=2)}")
        else:
            print(f"Error response: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_historical_api() 