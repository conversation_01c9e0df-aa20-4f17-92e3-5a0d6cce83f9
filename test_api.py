#!/usr/bin/env python3
"""
Test script to debug Polygon API historical data calls
"""

import requests
from datetime import date, timedelta
import json
import os
from dotenv import load_dotenv

from constants import (
    POLYGON_BASE_URL, POLYGON_AGGREGATES, INDEX_PREFIX, DATE_FORMAT_API,
    API_PARAM_ADJUSTED, API_PARAM_SORT_ASC, API_PARAM_LIMIT_MAX,
    TEST_SYMBOL_SPX, TEST_HISTORICAL_DAYS
)
from config import config

# Load environment variables
load_dotenv()

# Get API key from config
API_KEY = config.polygon_api_key

def test_historical_api():
    """Test the historical data API call"""
    
    # Calculate dates
    end_date = date.today()
    start_date = end_date - timedelta(days=TEST_HISTORICAL_DAYS)  # Testing period

    start_str = start_date.strftime(DATE_FORMAT_API)
    end_str = end_date.strftime(DATE_FORMAT_API)

    # Test with SPX (index)
    symbol = TEST_SYMBOL_SPX
    api_symbol = f"{INDEX_PREFIX}{symbol.upper()}"
    url = f"{POLYGON_BASE_URL}{POLYGON_AGGREGATES}/{api_symbol}/range/1/day/{start_str}/{end_str}"

    params = {
        'adjusted': API_PARAM_ADJUSTED,
        'sort': API_PARAM_SORT_ASC,
        'limit': API_PARAM_LIMIT_MAX,
        'apikey': API_KEY
    }
    
    print(f"Testing API call for {symbol}")
    print(f"URL: {url}")
    print(f"Params: {params}")
    print(f"Date range: {start_str} to {end_str}")
    print("-" * 60)
    
    try:
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response Status: {data.get('status')}")
            print(f"Results Count: {len(data.get('results', []))}")
            
            if data.get('results'):
                print(f"First result: {data['results'][0]}")
                print(f"Last result: {data['results'][-1]}")
            else:
                print("No results found")
                print(f"Full response: {json.dumps(data, indent=2)}")
        else:
            print(f"Error response: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_historical_api() 