#!/usr/bin/env python3
"""
Configuration Module
Centralized configuration settings for the options trading system.
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from constants import (
    DEFAULT_RISK_FREE_RATE, DEFAULT_VOLATILITY, DEFAULT_SPREAD_WIDTH,
    DEFAULT_PRICE_RANGE, DEFAULT_INDEX_SYMBOL, ENV_POLYGON_API_KEY,
    ENV_OPENAI_API_KEY, DEFAULT_API_KEY_PLACEHOLDER, VRP_LOOKBACK_DAYS,
    VRP_SHORT_PERIOD, VRP_LONG_PERIOD, TRADING_DAYS_IN_YEAR,
    DEFAULT_BINOMIAL_STEPS, DEFAULT_HESTON_PATHS, HESTON_DEFAULT_KAPPA,
    HESTON_DEFAULT_SIGMA_V, HESTON_DEFAULT_RHO, REALTIME_PRICE_THRESHOLD,
    REALTIME_CONNECTION_TIMEOUT, REALTIME_RECONNECT_DELAY
)

# Load environment variables
load_dotenv()


class Config:
    """Configuration class for the options trading system."""
    
    def __init__(self):
        """Initialize configuration with default values and environment overrides."""
        self._load_api_configuration()
        self._load_trading_configuration()
        self._load_vrp_configuration()
        self._load_pricing_configuration()
        self._load_realtime_configuration()
        self._load_logging_configuration()
    
    def _load_api_configuration(self):
        """Load API-related configuration."""
        self.polygon_api_key = self._get_api_key(ENV_POLYGON_API_KEY)
        self.openai_api_key = os.getenv(ENV_OPENAI_API_KEY)
        
        # API request settings
        self.api_timeout = int(os.getenv('API_TIMEOUT', '30'))
        self.api_retry_attempts = int(os.getenv('API_RETRY_ATTEMPTS', '3'))
        self.api_retry_delay = float(os.getenv('API_RETRY_DELAY', '1.0'))
    
    def _load_trading_configuration(self):
        """Load trading-related configuration."""
        self.default_symbol = os.getenv('DEFAULT_SYMBOL', DEFAULT_INDEX_SYMBOL)
        self.default_risk_free_rate = float(os.getenv('RISK_FREE_RATE', str(DEFAULT_RISK_FREE_RATE)))
        self.default_volatility = float(os.getenv('DEFAULT_VOLATILITY', str(DEFAULT_VOLATILITY)))
        self.default_spread_width = int(os.getenv('DEFAULT_SPREAD_WIDTH', str(DEFAULT_SPREAD_WIDTH)))
        self.default_price_range = int(os.getenv('DEFAULT_PRICE_RANGE', str(DEFAULT_PRICE_RANGE)))
        
        # Trading hours (in UTC)
        self.market_open_hour = int(os.getenv('MARKET_OPEN_HOUR', '14'))  # 9:30 AM EST
        self.market_close_hour = int(os.getenv('MARKET_CLOSE_HOUR', '21'))  # 4:00 PM EST
    
    def _load_vrp_configuration(self):
        """Load VRP analysis configuration."""
        self.vrp_lookback_days = int(os.getenv('VRP_LOOKBACK_DAYS', str(VRP_LOOKBACK_DAYS)))
        self.vrp_short_period = int(os.getenv('VRP_SHORT_PERIOD', str(VRP_SHORT_PERIOD)))
        self.vrp_long_period = int(os.getenv('VRP_LONG_PERIOD', str(VRP_LONG_PERIOD)))
        self.trading_days_per_year = int(os.getenv('TRADING_DAYS_PER_YEAR', str(TRADING_DAYS_IN_YEAR)))
        
        # VRP thresholds (can be overridden via environment)
        self.vrp_strong_sell_threshold = float(os.getenv('VRP_STRONG_SELL_THRESHOLD', '0.08'))
        self.vrp_sell_threshold = float(os.getenv('VRP_SELL_THRESHOLD', '0.04'))
        self.vrp_buy_threshold = float(os.getenv('VRP_BUY_THRESHOLD', '-0.04'))
        self.vrp_strong_buy_threshold = float(os.getenv('VRP_STRONG_BUY_THRESHOLD', '-0.08'))
    
    def _load_pricing_configuration(self):
        """Load option pricing model configuration."""
        self.binomial_steps = int(os.getenv('BINOMIAL_STEPS', str(DEFAULT_BINOMIAL_STEPS)))
        self.heston_paths = int(os.getenv('HESTON_PATHS', str(DEFAULT_HESTON_PATHS)))
        
        # Heston model parameters
        self.heston_kappa = float(os.getenv('HESTON_KAPPA', str(HESTON_DEFAULT_KAPPA)))
        self.heston_sigma_v = float(os.getenv('HESTON_SIGMA_V', str(HESTON_DEFAULT_SIGMA_V)))
        self.heston_rho = float(os.getenv('HESTON_RHO', str(HESTON_DEFAULT_RHO)))
        
        # Volatility estimation settings
        self.iv_estimation_tolerance = float(os.getenv('IV_ESTIMATION_TOLERANCE', '1e-6'))
        self.iv_estimation_max_iterations = int(os.getenv('IV_ESTIMATION_MAX_ITERATIONS', '100'))
    
    def _load_realtime_configuration(self):
        """Load real-time streaming configuration."""
        self.realtime_enabled = os.getenv('REALTIME_ENABLED', 'false').lower() == 'true'
        self.realtime_price_threshold = float(os.getenv('REALTIME_PRICE_THRESHOLD', str(REALTIME_PRICE_THRESHOLD)))
        self.realtime_connection_timeout = int(os.getenv('REALTIME_CONNECTION_TIMEOUT', str(REALTIME_CONNECTION_TIMEOUT)))
        self.realtime_reconnect_delay = int(os.getenv('REALTIME_RECONNECT_DELAY', str(REALTIME_RECONNECT_DELAY)))
        
        # WebSocket settings
        self.websocket_ping_interval = int(os.getenv('WEBSOCKET_PING_INTERVAL', '20'))
        self.websocket_ping_timeout = int(os.getenv('WEBSOCKET_PING_TIMEOUT', '10'))
    
    def _load_logging_configuration(self):
        """Load logging configuration."""
        self.log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        self.log_format = os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.log_file = os.getenv('LOG_FILE', 'options_trading.log')
        self.log_max_bytes = int(os.getenv('LOG_MAX_BYTES', '10485760'))  # 10MB
        self.log_backup_count = int(os.getenv('LOG_BACKUP_COUNT', '5'))
    
    def _get_api_key(self, env_var_name: str) -> str:
        """
        Get API key from environment variables with validation.
        
        Args:
            env_var_name (str): Environment variable name
            
        Returns:
            str: API key
            
        Raises:
            ValueError: If API key is not set or is placeholder
        """
        api_key = os.getenv(env_var_name)
        if not api_key or api_key == DEFAULT_API_KEY_PLACEHOLDER:
            raise ValueError(f"Please set your {env_var_name} in the .env file")
        return api_key
    
    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading-related configuration as dictionary."""
        return {
            'default_symbol': self.default_symbol,
            'default_risk_free_rate': self.default_risk_free_rate,
            'default_volatility': self.default_volatility,
            'default_spread_width': self.default_spread_width,
            'default_price_range': self.default_price_range,
            'market_open_hour': self.market_open_hour,
            'market_close_hour': self.market_close_hour
        }
    
    def get_vrp_config(self) -> Dict[str, Any]:
        """Get VRP analysis configuration as dictionary."""
        return {
            'lookback_days': self.vrp_lookback_days,
            'short_period': self.vrp_short_period,
            'long_period': self.vrp_long_period,
            'trading_days_per_year': self.trading_days_per_year,
            'strong_sell_threshold': self.vrp_strong_sell_threshold,
            'sell_threshold': self.vrp_sell_threshold,
            'buy_threshold': self.vrp_buy_threshold,
            'strong_buy_threshold': self.vrp_strong_buy_threshold
        }
    
    def get_pricing_config(self) -> Dict[str, Any]:
        """Get pricing model configuration as dictionary."""
        return {
            'binomial_steps': self.binomial_steps,
            'heston_paths': self.heston_paths,
            'heston_kappa': self.heston_kappa,
            'heston_sigma_v': self.heston_sigma_v,
            'heston_rho': self.heston_rho,
            'iv_estimation_tolerance': self.iv_estimation_tolerance,
            'iv_estimation_max_iterations': self.iv_estimation_max_iterations
        }
    
    def get_realtime_config(self) -> Dict[str, Any]:
        """Get real-time streaming configuration as dictionary."""
        return {
            'enabled': self.realtime_enabled,
            'price_threshold': self.realtime_price_threshold,
            'connection_timeout': self.realtime_connection_timeout,
            'reconnect_delay': self.realtime_reconnect_delay,
            'websocket_ping_interval': self.websocket_ping_interval,
            'websocket_ping_timeout': self.websocket_ping_timeout
        }
    
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, self.log_level),
            format=self.log_format,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.log_file)
            ]
        )
    
    def validate_configuration(self) -> bool:
        """
        Validate the configuration settings.
        
        Returns:
            bool: True if configuration is valid
            
        Raises:
            ValueError: If configuration is invalid
        """
        # Validate API keys
        if not self.polygon_api_key:
            raise ValueError("Polygon API key is required")
        
        # Validate numeric ranges
        if not 0 < self.default_risk_free_rate < 1:
            raise ValueError("Risk-free rate must be between 0 and 1")
        
        if not 0 < self.default_volatility < 5:
            raise ValueError("Default volatility must be between 0 and 5")
        
        if self.vrp_lookback_days < 30:
            raise ValueError("VRP lookback days must be at least 30")
        
        if self.binomial_steps < 10:
            raise ValueError("Binomial steps must be at least 10")
        
        if self.heston_paths < 1000:
            raise ValueError("Heston paths must be at least 1000")
        
        return True


# Global configuration instance
config = Config()
