#!/usr/bin/env python3
"""
Spread Analyzer Module
Handles options spread calculations, pricing, and analysis
"""

import logging
from typing import Dict, List, Any
from market_data import MarketData
from pricing_engine import PricingEngine
from vrp_analyzer import VRPAnalyzer


class SpreadAnalyzer:
    """Handle options spread analysis and calculations."""
    
    def __init__(self, market_data: MarketData, pricing_engine: PricingEngine):
        """
        Initialize SpreadAnalyzer.
        
        Args:
            market_data (MarketData): Market data handler
            pricing_engine (PricingEngine): Pricing engine for calculations
        """
        self.market_data = market_data
        self.pricing_engine = pricing_engine
        self.vrp_analyzer = VRPAnalyzer(market_data, pricing_engine)
    
    def calculate_fallback_price(self, option: Dict[str, Any], underlying_price: float) -> float:
        """Calculate fallback option price when API data is unavailable."""
        if option['option_type'] == 'call':
            theoretical_price = max(underlying_price - option['strike'], 0) + 2.5
        else:  # put
            theoretical_price = max(option['strike'] - underlying_price, 0) + 1.5
        return round(theoretical_price, 2)
    
    def price_options(self, options_data: List[Dict[str, Any]], underlying_price: float) -> List[Dict[str, Any]]:
        """
        Price options using market data and theoretical models.
        
        Args:
            options_data (List[Dict[str, Any]]): Options data to price
            underlying_price (float): Current price of underlying asset
        
        Returns:
            List[Dict[str, Any]]: Options data with market prices and theoretical pricing
        """
        priced_options = []
        
        for option in options_data:
            priced_option = option.copy()
            option_symbol = option['symbol']
            
            try:
                # Get real-time option quote from Polygon API
                quote_url = f"https://api.polygon.io/v3/quotes/{option_symbol}"
                quote_params = {
                    'apikey': self.market_data.api_key,
                    'limit': 1
                }
                
                import requests
                quote_response = requests.get(quote_url, params=quote_params)
                quote_response.raise_for_status()
                quote_data = quote_response.json()
                
                # Update prices with real market data
                if quote_data.get('status') == 'OK' and quote_data.get('results'):
                    quote_result = quote_data['results'][0]
                    priced_option.update({
                        'bid': float(quote_result.get('bid_price', 0)),
                        'ask': float(quote_result.get('ask_price', 0)),
                        'last': float(quote_result.get('last_price', 0)),
                        'bid_size': int(quote_result.get('bid_size', 0)),
                        'ask_size': int(quote_result.get('ask_size', 0)),
                        'timestamp': quote_result.get('sip_timestamp', 0)
                    })
                    
                    # Use mid-price as theoretical price if available
                    bid = priced_option['bid']
                    ask = priced_option['ask']
                    if bid > 0 and ask > 0:
                        mid_price = (bid + ask) / 2
                        priced_option['market_mid'] = round(mid_price, 2)
                        priced_option['price_source'] = 'market_mid'
                    elif priced_option['last'] > 0:
                        priced_option['market_mid'] = priced_option['last']
                        priced_option['price_source'] = 'market_last'
                    else:
                        # Fall back to calculated price
                        priced_option['market_mid'] = self.calculate_fallback_price(option, underlying_price)
                        priced_option['price_source'] = 'calculated'
                else:
                    # Fall back to calculated price
                    priced_option['market_mid'] = self.calculate_fallback_price(option, underlying_price)
                    priced_option['price_source'] = 'calculated'
                
                # Calculate theoretical prices and Greeks
                time_to_exp = self.pricing_engine.get_time_to_expiration(option['expiration'])
                
                # Estimate implied volatility from market price if available
                market_price = priced_option.get('market_mid', 0)
                if market_price > 0 and time_to_exp > 0:
                    estimated_iv = self.pricing_engine.estimate_implied_volatility(
                        market_price, underlying_price, option['strike'], 
                        time_to_exp, option['option_type']
                    )
                    priced_option['estimated_iv'] = estimated_iv
                else:
                    estimated_iv = 0.20  # Use default volatility
                    priced_option['estimated_iv'] = estimated_iv
                
                # Calculate Black-Scholes price with estimated IV
                if option['option_type'].lower() == 'call':
                    bs_price = self.pricing_engine.black_scholes_call(
                        underlying_price, option['strike'], time_to_exp, estimated_iv
                    )
                else:  # put
                    bs_price = self.pricing_engine.black_scholes_put(
                        underlying_price, option['strike'], time_to_exp, estimated_iv
                    )
                
                priced_option['black_scholes_price'] = round(bs_price, 2)
                
                # Calculate Black-Scholes Greeks
                bs_greeks = self.pricing_engine.black_scholes_greeks(
                    underlying_price, option['strike'], time_to_exp, 
                    estimated_iv, option['option_type']
                )
                priced_option['bs_delta'] = bs_greeks['delta']
                priced_option['bs_gamma'] = bs_greeks['gamma']
                priced_option['bs_theta'] = bs_greeks['theta']
                priced_option['bs_vega'] = bs_greeks['vega']
                
                # Calculate Binomial price with estimated IV
                binomial_price = self.pricing_engine.binomial_option_price(
                    underlying_price, option['strike'], time_to_exp, 
                    estimated_iv, option['option_type']
                )
                priced_option['binomial_price'] = round(binomial_price, 2)
                
                # Calculate Binomial Greeks
                binomial_greeks_calc = self.pricing_engine.binomial_greeks(
                    underlying_price, option['strike'], time_to_exp, 
                    estimated_iv, option['option_type']
                )
                priced_option['binomial_delta'] = binomial_greeks_calc['delta']
                priced_option['binomial_gamma'] = binomial_greeks_calc['gamma']
                priced_option['binomial_theta'] = binomial_greeks_calc['theta']
                priced_option['binomial_vega'] = binomial_greeks_calc['vega']
                
                # Calculate Heston price with estimated IV
                heston_price = self.pricing_engine.heston_option_price(
                    underlying_price, option['strike'], time_to_exp, 
                    estimated_iv, option['option_type']
                )
                priced_option['heston_price'] = round(heston_price, 2)
                
                # Calculate Heston Greeks
                heston_greeks_calc = self.pricing_engine.heston_greeks(
                    underlying_price, option['strike'], time_to_exp, 
                    estimated_iv, option['option_type']
                )
                priced_option['heston_delta'] = heston_greeks_calc['delta']
                priced_option['heston_gamma'] = heston_greeks_calc['gamma']
                priced_option['heston_theta'] = heston_greeks_calc['theta']
                priced_option['heston_vega'] = heston_greeks_calc['vega']
                
                # Calculate price differences (market vs all theoretical models)
                if market_price > 0:
                    bs_price_diff = market_price - bs_price
                    bs_price_diff_pct = (bs_price_diff / bs_price) * 100 if bs_price > 0 else 0
                    priced_option['bs_price_difference'] = round(bs_price_diff, 2)
                    priced_option['bs_price_difference_pct'] = round(bs_price_diff_pct, 2)
                    
                    binomial_price_diff = market_price - binomial_price
                    binomial_price_diff_pct = (binomial_price_diff / binomial_price) * 100 if binomial_price > 0 else 0
                    priced_option['binomial_price_difference'] = round(binomial_price_diff, 2)
                    priced_option['binomial_price_difference_pct'] = round(binomial_price_diff_pct, 2)
                    
                    heston_price_diff = market_price - heston_price
                    heston_price_diff_pct = (heston_price_diff / heston_price) * 100 if heston_price > 0 else 0
                    priced_option['heston_price_difference'] = round(heston_price_diff, 2)
                    priced_option['heston_price_difference_pct'] = round(heston_price_diff_pct, 2)
                    
                    # Legacy field for backward compatibility
                    priced_option['price_difference'] = round(bs_price_diff, 2)
                    priced_option['price_difference_pct'] = round(bs_price_diff_pct, 2)
                else:
                    priced_option['bs_price_difference'] = 0
                    priced_option['bs_price_difference_pct'] = 0
                    priced_option['binomial_price_difference'] = 0
                    priced_option['binomial_price_difference_pct'] = 0
                    priced_option['heston_price_difference'] = 0
                    priced_option['heston_price_difference_pct'] = 0
                    priced_option['price_difference'] = 0
                    priced_option['price_difference_pct'] = 0
                
                # For backward compatibility, set theoretical_price to market_mid
                priced_option['theoretical_price'] = priced_option['market_mid']
                
                # Add fallback Greeks
                fallback_greeks = self.pricing_engine.calculate_fallback_greeks(option, underlying_price)
                priced_option.update({
                    'delta': fallback_greeks['delta'],
                    'gamma': fallback_greeks['gamma'], 
                    'theta': fallback_greeks['theta'],
                    'vega': fallback_greeks['vega'],
                    'greeks_source': 'calculated'
                })
                    
            except Exception:
                # Fall back to calculated values
                priced_option['market_mid'] = self.calculate_fallback_price(option, underlying_price)
                priced_option['price_source'] = 'calculated'
                fallback_greeks = self.pricing_engine.calculate_fallback_greeks(option, underlying_price)
                priced_option.update(fallback_greeks)
                priced_option['greeks_source'] = 'calculated'
            
            priced_options.append(priced_option)
        
        return priced_options
    
    def price_spread(self, long_leg: Dict[str, Any], short_leg: Dict[str, Any], 
                    strategy_type: str = "bear_put_spread") -> Dict[str, Any]:
        """
        Price an options spread strategy with proper long/short positioning.
        
        Args:
            long_leg (Dict[str, Any]): Long position (we buy this option)
            short_leg (Dict[str, Any]): Short position (we sell this option)
            strategy_type (str): Type of spread strategy
        
        Returns:
            Dict[str, Any]: Spread pricing information
        """
        # Calculate net debit (what we pay)
        # We pay for the long leg and receive premium for the short leg
        long_price = long_leg.get('market_mid', long_leg['last'])
        short_price = short_leg.get('market_mid', short_leg['last'])
        
        net_debit = long_price - short_price  # Debit spread
        
        if strategy_type == "bear_put_spread":
            # For a bear put spread (long higher strike put, short lower strike put)
            # Max profit = strike difference - net debit
            # Max loss = net debit
            # Breakeven = higher strike - net debit
            strike_diff = abs(long_leg['strike'] - short_leg['strike'])
            max_profit = strike_diff - net_debit
            max_loss = net_debit
            breakeven = max(long_leg['strike'], short_leg['strike']) - net_debit
        elif strategy_type == "bull_call_spread":
            # For a bull call spread (long higher strike call, short lower strike call)
            # Max profit = strike difference - net debit
            # Max loss = net debit
            # Breakeven = lower strike + net debit
            strike_diff = abs(long_leg['strike'] - short_leg['strike'])
            max_profit = strike_diff - net_debit
            max_loss = net_debit
            breakeven = min(long_leg['strike'], short_leg['strike']) + net_debit
        else:
            # Generic calculation
            strike_diff = abs(long_leg['strike'] - short_leg['strike'])
            max_profit = strike_diff - abs(net_debit)
            max_loss = abs(net_debit)
            breakeven = min(long_leg['strike'], short_leg['strike']) + abs(net_debit)
        
        # Calculate expected return as percentage
        expected_return = (max_profit / max_loss * 100) if max_loss > 0 else 0
        
        spread_data = {
            'strategy_type': strategy_type,
            'long_leg': long_leg,
            'short_leg': short_leg,
            'net_debit': round(net_debit, 2),
            'max_profit': round(max_profit, 2),
            'max_loss': round(max_loss, 2),
            'breakeven': round(breakeven, 2),
            'probability_profit': 0.45,  # Stub probability
            'expected_return': round(expected_return, 1)
        }
        
        return spread_data
    
    def analyze_spread(self, ticker: str, current_price: float, exp_month: int, exp_year: int, 
                      option_type: str = "put", spread_width: float = 100, 
                      price_range: float = 10, display_vrp_console: bool = False) -> Dict[str, Any]:
        """
        Analyze options spread with market vs theoretical pricing and VRP analysis.
        
        Args:
            ticker (str): Stock/index ticker symbol
            current_price (float): Current price of underlying
            exp_month (int): Expiration month (1-12)
            exp_year (int): Expiration year
            option_type (str): 'put' for bear put spread, 'call' for bull call spread
            spread_width (float): Strike price spread width in points (default: 100)
            price_range (float): Price range for scenario analysis in points (default: 10)
            display_vrp_console (bool): Whether to display VRP analysis to console (default: False)
        
        Returns:
            Dict[str, Any]: Spread analysis results including market, theoretical pricing, and VRP analysis
        """
        try:
            # Calculate strikes based on current price and option type
            if option_type.lower() == "put":
                # Bear put spread: Long at-the-money, Short spread_width points lower
                long_strike = round(current_price / spread_width) * spread_width  # Round to nearest spread_width
                short_strike = long_strike - spread_width
                strategy_name = "BEAR PUT SPREAD"
            elif option_type.lower() == "call":
                # Bull call spread: Long at-the-money, Short spread_width points higher  
                long_strike = round(current_price / spread_width) * spread_width  # Round to nearest spread_width
                short_strike = long_strike + spread_width
                strategy_name = "BULL CALL SPREAD"
            else:
                raise ValueError("option_type must be 'put' or 'call'")
            
            # Get expiration date
            expiration_date = self.market_data.get_third_friday(exp_year, exp_month)
            
            # Perform VRP analysis
            if display_vrp_console:
                print("🎯 Performing VRP analysis...")
            vrp_data = self.vrp_analyzer.get_vrp_for_spread_analysis(ticker, current_price, exp_month, exp_year)
            
            # Display VRP analysis to console only if requested
            if display_vrp_console:
                self.vrp_analyzer.display_vrp_analysis(vrp_data['vrp_signal'])
            
            # Retrieve options data for both strikes
            options_data_long = self.market_data.retrieve_options_by_date(ticker, long_strike, expiration_date)
            options_data_short = self.market_data.retrieve_options_by_date(ticker, short_strike, expiration_date)
            
            # Combine the options data
            all_options = options_data_long + options_data_short
            
            # Price the options
            priced_options = self.price_options(all_options, current_price)
            
            # Find the specific options we need
            long_option = next((opt for opt in priced_options 
                               if opt['option_type'] == option_type.lower() and opt['strike'] == long_strike), None)
            short_option = next((opt for opt in priced_options 
                                if opt['option_type'] == option_type.lower() and opt['strike'] == short_strike), None)
            
            if not long_option or not short_option:
                raise ValueError(f"Could not find matching {option_type} options for both strikes")
            
            # Calculate spread
            if option_type.lower() == "put":
                spread_analysis = self.price_spread(long_option, short_option, strategy_type="bear_put_spread")
            else:
                spread_analysis = self.price_spread(long_option, short_option, strategy_type="bull_call_spread")
            
            # Add VRP-based strategy recommendation
            vrp_recommendation = self._get_vrp_strategy_recommendation(vrp_data, option_type, spread_analysis)
            
            return {
                'ticker': ticker,
                'current_price': current_price,
                'strategy': strategy_name,
                'long_option': long_option,
                'short_option': short_option,
                'spread_analysis': spread_analysis,
                'vrp_analysis': vrp_data,
                'vrp_recommendation': vrp_recommendation,
                'expiration_date': expiration_date,
                'spread_width': spread_width,
                'price_range': price_range
            }
            
        except Exception as e:
            print(f"Error analyzing spread: {str(e)}")
            raise
    
    def _get_vrp_strategy_recommendation(self, vrp_data: Dict[str, Any], option_type: str, 
                                       spread_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate VRP-based strategy recommendation.
        
        Args:
            vrp_data (Dict[str, Any]): VRP analysis data
            option_type (str): 'put' or 'call'
            spread_analysis (Dict[str, Any]): Spread analysis results
            
        Returns:
            Dict[str, Any]: VRP-based recommendation
        """
        vrp_signal = vrp_data['vrp_signal']
        is_expensive = vrp_data['is_options_expensive']
        is_cheap = vrp_data['is_options_cheap']
        
        # Determine if current strategy aligns with VRP
        is_selling_strategy = option_type.lower() == "put"  # Bear put spread sells volatility
        is_buying_strategy = option_type.lower() == "call"  # Bull call spread buys volatility
        
        recommendation = {
            'vrp_alignment': 'neutral',
            'confidence': vrp_signal.confidence,
            'reasoning': '',
            'alternative_strategy': '',
            'risk_assessment': 'moderate'
        }
        
        if is_expensive and is_selling_strategy:
            recommendation['vrp_alignment'] = 'favorable'
            recommendation['reasoning'] = f"VRP of {vrp_signal.vrp_30d:+.1%} suggests options are overpriced. Bear put spread benefits from high volatility premium."
            recommendation['risk_assessment'] = 'low'
        elif is_expensive and is_buying_strategy:
            recommendation['vrp_alignment'] = 'unfavorable'
            recommendation['reasoning'] = f"VRP of {vrp_signal.vrp_30d:+.1%} suggests options are overpriced. Consider selling strategies instead."
            recommendation['alternative_strategy'] = "Bear put spread or iron condor"
            recommendation['risk_assessment'] = 'high'
        elif is_cheap and is_buying_strategy:
            recommendation['vrp_alignment'] = 'favorable'
            recommendation['reasoning'] = f"VRP of {vrp_signal.vrp_30d:+.1%} suggests options are underpriced. Bull call spread benefits from low volatility cost."
            recommendation['risk_assessment'] = 'low'
        elif is_cheap and is_selling_strategy:
            recommendation['vrp_alignment'] = 'unfavorable'
            recommendation['reasoning'] = f"VRP of {vrp_signal.vrp_30d:+.1%} suggests options are underpriced. Consider buying strategies instead."
            recommendation['alternative_strategy'] = "Bull call spread or long straddle"
            recommendation['risk_assessment'] = 'high'
        else:
            recommendation['reasoning'] = f"VRP of {vrp_signal.vrp_30d:+.1%} suggests neutral volatility environment. Strategy is reasonably aligned."
        
        return recommendation 