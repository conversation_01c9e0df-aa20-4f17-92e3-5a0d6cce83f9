#!/usr/bin/env python3
"""
Options Trading Routine
Main module for retrieving options data, pricing options, and pricing spreads.
Uses modular class architecture for better maintainability.
"""

import logging
import argparse
from typing import Dict, Any

from constants import (
    DEFAULT_INDEX_SYMBOL, DEFAULT_SPREAD_WIDTH, DEFAULT_PRICE_RANGE,
    DEFAULT_RISK_FREE_RATE, TEST_MONTH, TEST_YEAR
)
from config import config
from market_data import MarketData
from pricing_engine import PricingEngine
from spread_analyzer import SpreadAnalyzer
from report_generator import ReportGenerator


def main():
    """
    Main entry point for options spread analysis.
    
    Compares market prices against Black-Scholes, Binomial, and Heston theoretical pricing models.
    
    Usage examples:
    - Default SPX put spread: python main.py
    - Custom parameters: python main.py --ticker AAPL --price 175 --month 12 --year 2024 --type call
    """
    # Set up command line argument parsing
    parser = argparse.ArgumentParser(description='Analyze options spreads with market vs theoretical pricing')
    parser.add_argument('--ticker', '-t', type=str, default=DEFAULT_INDEX_SYMBOL,
                       help=f'Stock/index ticker symbol (default: {DEFAULT_INDEX_SYMBOL})')
    parser.add_argument('--price', '-p', type=float, default=None,
                       help='Current price of underlying (default: fetch from API)')
    parser.add_argument('--month', '-m', type=int, default=TEST_MONTH,
                       help=f'Expiration month 1-12 (default: {TEST_MONTH})')
    parser.add_argument('--year', '-y', type=int, default=TEST_YEAR,
                       help=f'Expiration year (default: {TEST_YEAR})')
    parser.add_argument('--rate', '-r', type=float, default=DEFAULT_RISK_FREE_RATE,
                       help=f'Risk-free rate (default: {DEFAULT_RISK_FREE_RATE})')
    parser.add_argument('--spread-width', '-w', type=float, default=DEFAULT_SPREAD_WIDTH,
                       help=f'Strike price spread width in points (default: {DEFAULT_SPREAD_WIDTH})')
    parser.add_argument('--price-range', '-g', type=float, default=DEFAULT_PRICE_RANGE,
                       help=f'Price range for scenario analysis in points (default: {DEFAULT_PRICE_RANGE})')
    parser.add_argument('--index', '-i', action='store_true', default=True,
                       help='Treat ticker as index (adds i: prefix for API) (default: True)')
    parser.add_argument('--stock', '-s', dest='index', action='store_false',
                       help='Treat ticker as stock (no i: prefix for API)')
    parser.add_argument('--realtime', dest='realtime', action='store_true',
                       help='Enable real-time streaming for index prices')
    parser.add_argument('--no-realtime', dest='realtime', action='store_false',
                       help='Disable real-time streaming (use API calls only)')
    parser.set_defaults(realtime=False)
    
    args = parser.parse_args()
    
    try:
        # Initialize components
        market_data = MarketData()
        pricing_engine = PricingEngine(risk_free_rate=args.rate)
        spread_analyzer = SpreadAnalyzer(market_data, pricing_engine)
        report_generator = ReportGenerator()
        
        # Enable real-time streaming if requested
        if args.realtime and args.index:
            print(f"🚀 Enabling real-time streaming for {args.ticker}")
            market_data.enable_realtime_streaming([args.ticker])
            print("💡 Real-time prices will be used when available")
        
        # Get current price from API first, use parameter as fallback
        try:
            current_price = market_data.get_current_price(args.ticker, args.index, use_realtime=args.realtime)
            if args.price is not None:
                print(f"📊 API Price: ${current_price:.2f}, Parameter Price: ${args.price:.2f}")
                print(f"💡 Using API price: ${current_price:.2f}")
            else:
                print(f"📊 Retrieved current price from API: ${current_price:.2f}")
        except Exception as e:
            if args.price is not None:
                current_price = args.price
                print(f"⚠️  API price retrieval failed: {str(e)}")
                print(f"💡 Using provided price: ${current_price:.2f}")
            else:
                print(f"❌ API price retrieval failed: {str(e)}")
                print(f"💡 Please provide current price using --price parameter")
                raise Exception(f"No price available for {args.ticker}. Please use --price parameter.")
        
        # Analyze both put and call spreads
        put_result = spread_analyzer.analyze_spread(
            ticker=args.ticker,
            current_price=current_price,
            exp_month=args.month,
            exp_year=args.year,
            option_type="put",
            spread_width=args.spread_width,
            price_range=args.price_range
        )
        
        call_result = spread_analyzer.analyze_spread(
            ticker=args.ticker,
            current_price=current_price,
            exp_month=args.month,
            exp_year=args.year,
            option_type="call",
            spread_width=args.spread_width,
            price_range=args.price_range
        )
        
        # Generate consolidated HTML report
        html_file = report_generator.generate_consolidated_html_report(
            put_result, call_result, args.ticker, current_price
        )
        
        return {'put_analysis': put_result, 'call_analysis': call_result, 'html_file': html_file}
        
    except Exception as e:
        print(f"Error in analysis: {str(e)}")
        raise
    finally:
        # Clean up real-time streaming if it was enabled
        if 'args' in locals() and getattr(args, 'realtime', False) and 'market_data' in locals():
            market_data.stop_realtime_streaming()


def quick_analysis(ticker: str = "SPX", price: float = None, month: int = 7, 
                  year: int = 2025, option_type: str = "put", spread_width: float = 100, 
                  price_range: float = 10, is_index: bool = True) -> Dict[str, Any]:
    """
    Quick analysis function for programmatic use.
    
    Args:
        ticker (str): Stock/index ticker symbol
        price (float): Current price (None to fetch from API, required if API fails)
        month (int): Expiration month (1-12)
        year (int): Expiration year
        option_type (str): 'put' or 'call'
        spread_width (float): Strike price spread width in points
        price_range (float): Price range for scenario analysis in points
        is_index (bool): Whether ticker is an index (default: True)
    
    Returns:
        Dict[str, Any]: Analysis results
        
    Raises:
        Exception: If API fails and no price is provided
    """
    # Initialize components
    market_data = MarketData()
    pricing_engine = PricingEngine()
    spread_analyzer = SpreadAnalyzer(market_data, pricing_engine)
    
    if price is None:
        try:
            price = market_data.get_current_price(ticker, is_index)
        except Exception as e:
            raise Exception(f"API failed to retrieve price for {ticker} and no price provided: {str(e)}")
    
    return spread_analyzer.analyze_spread(ticker, price, month, year, option_type, 
                                        spread_width=spread_width, price_range=price_range)


if __name__ == "__main__":
    main() 