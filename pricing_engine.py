#!/usr/bin/env python3
"""
Pricing Engine Module
Handles all option pricing models including Black-Scholes, Binomial, and <PERSON>ston
"""

import math
import numpy as np
from scipy.stats import norm
from typing import Dict, List, Any
from datetime import datetime, date


class PricingEngine:
    """Handle option pricing using multiple models."""
    
    def __init__(self, risk_free_rate: float = 0.05):
        """
        Initialize PricingEngine.
        
        Args:
            risk_free_rate (float): Risk-free interest rate (default: 0.05)
        """
        self.risk_free_rate = risk_free_rate
    
    def get_time_to_expiration(self, expiration_date: str) -> float:
        """
        Calculate time to expiration in years.
        
        Args:
            expiration_date (str): Expiration date in YYYY-MM-DD format
        
        Returns:
            float: Time to expiration in years
        """
        exp_date = datetime.strptime(expiration_date, '%Y-%m-%d').date()
        today = date.today()
        days_to_exp = (exp_date - today).days
        return max(days_to_exp / 365.0, 0.0)  # Convert to years, min 0
    
    def estimate_implied_volatility(self, market_price: float, S: float, K: float, 
                                  T: float, option_type: str) -> float:
        """
        Simple estimation of implied volatility using Newton-Raphson method.
        
        Args:
            market_price (float): Market price of the option
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            option_type (str): 'call' or 'put'
        
        Returns:
            float: Estimated implied volatility
        """
        if T <= 0 or market_price <= 0:
            return 0.20  # Default 20% volatility
        
        # Initial guess
        vol = 0.20
        
        # Newton-Raphson iterations
        for i in range(50):  # Max 50 iterations
            if option_type.lower() == 'call':
                price = self.black_scholes_call(S, K, T, vol)
            else:
                price = self.black_scholes_put(S, K, T, vol)
            
            # Calculate vega for Newton-Raphson
            if vol <= 0:
                vol = 0.01
                continue
                
            d1 = (math.log(S / K) + (self.risk_free_rate + 0.5 * vol**2) * T) / (vol * math.sqrt(T))
            vega = S * norm.pdf(d1) * math.sqrt(T)
            
            if abs(vega) < 1e-8:
                break
                
            # Newton-Raphson update
            price_diff = price - market_price
            if abs(price_diff) < 0.001:  # Close enough
                break
                
            vol = vol - price_diff / vega
            
            # Keep volatility in reasonable bounds
            vol = max(0.001, min(vol, 5.0))
        
        return round(vol, 4)
    
    def black_scholes_call(self, S: float, K: float, T: float, sigma: float) -> float:
        """
        Calculate Black-Scholes call option price.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            sigma (float): Volatility
        
        Returns:
            float: Theoretical call option price
        """
        if T <= 0:
            return max(S - K, 0)
        
        d1 = (math.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        call_price = S * norm.cdf(d1) - K * math.exp(-self.risk_free_rate * T) * norm.cdf(d2)
        return call_price
    
    def black_scholes_put(self, S: float, K: float, T: float, sigma: float) -> float:
        """
        Calculate Black-Scholes put option price.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            sigma (float): Volatility
        
        Returns:
            float: Theoretical put option price
        """
        if T <= 0:
            return max(K - S, 0)
        
        d1 = (math.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        put_price = K * math.exp(-self.risk_free_rate * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
        return put_price
    
    def black_scholes_greeks(self, S: float, K: float, T: float, sigma: float, 
                           option_type: str) -> Dict[str, float]:
        """
        Calculate Black-Scholes Greeks.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            sigma (float): Volatility
            option_type (str): 'call' or 'put'
        
        Returns:
            Dict[str, float]: Dictionary with delta, gamma, theta, vega
        """
        if T <= 0:
            # At expiration
            if option_type.lower() == 'call':
                delta = 1.0 if S > K else 0.0
            else:  # put
                delta = -1.0 if S < K else 0.0
            
            return {
                'delta': delta,
                'gamma': 0.0,
                'theta': 0.0,
                'vega': 0.0
            }
        
        d1 = (math.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        # Common terms
        pdf_d1 = norm.pdf(d1)
        cdf_d1 = norm.cdf(d1)
        cdf_neg_d1 = norm.cdf(-d1)
        cdf_d2 = norm.cdf(d2)
        cdf_neg_d2 = norm.cdf(-d2)
        
        # Delta
        if option_type.lower() == 'call':
            delta = cdf_d1
        else:  # put
            delta = cdf_d1 - 1.0
        
        # Gamma (same for calls and puts)
        gamma = pdf_d1 / (S * sigma * math.sqrt(T))
        
        # Theta
        if option_type.lower() == 'call':
            theta = (-(S * pdf_d1 * sigma) / (2 * math.sqrt(T)) 
                     - self.risk_free_rate * K * math.exp(-self.risk_free_rate * T) * cdf_d2) / 365
        else:  # put
            theta = (-(S * pdf_d1 * sigma) / (2 * math.sqrt(T)) 
                     + self.risk_free_rate * K * math.exp(-self.risk_free_rate * T) * cdf_neg_d2) / 365
        
        # Vega (same for calls and puts)
        vega = S * pdf_d1 * math.sqrt(T) / 100  # Per 1% change in volatility
        
        return {
            'delta': round(delta, 4),
            'gamma': round(gamma, 4),
            'theta': round(theta, 4),
            'vega': round(vega, 4)
        }
    
    def binomial_option_price(self, S: float, K: float, T: float, sigma: float, 
                             option_type: str, n: int = 100) -> float:
        """
        Calculate option price using binomial tree model.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            sigma (float): Volatility
            option_type (str): 'call' or 'put'
            n (int): Number of time steps (default: 100)
        
        Returns:
            float: Binomial option price
        """
        if T <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        # Calculate parameters
        dt = T / n
        u = math.exp(sigma * math.sqrt(dt))  # Up factor
        d = 1 / u  # Down factor
        p = (math.exp(self.risk_free_rate * dt) - d) / (u - d)  # Risk-neutral probability
        
        # Initialize stock price tree
        stock_prices = []
        for i in range(n + 1):
            price = S * (u ** (n - i)) * (d ** i)
            stock_prices.append(price)
        
        # Initialize option values at expiration
        option_values = []
        for i in range(n + 1):
            if option_type.lower() == 'call':
                value = max(stock_prices[i] - K, 0)
            else:  # put
                value = max(K - stock_prices[i], 0)
            option_values.append(value)
        
        # Work backwards through the tree
        for j in range(n - 1, -1, -1):
            for i in range(j + 1):
                # Calculate continuation value
                continuation = math.exp(-self.risk_free_rate * dt) * (p * option_values[i] + (1 - p) * option_values[i + 1])
                option_values[i] = continuation
        
        return option_values[0]
    
    def binomial_greeks(self, S: float, K: float, T: float, sigma: float, 
                       option_type: str, n: int = 100) -> Dict[str, float]:
        """
        Calculate Greeks using binomial model with finite differences.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            sigma (float): Volatility
            option_type (str): 'call' or 'put'
            n (int): Number of time steps (default: 100)
        
        Returns:
            Dict[str, float]: Dictionary with delta, gamma, theta, vega
        """
        if T <= 0:
            # At expiration
            if option_type.lower() == 'call':
                delta = 1.0 if S > K else 0.0
            else:  # put
                delta = -1.0 if S < K else 0.0
            
            return {
                'delta': delta,
                'gamma': 0.0,
                'theta': 0.0,
                'vega': 0.0
            }
        
        # Base price
        price = self.binomial_option_price(S, K, T, sigma, option_type, n)
        
        # Delta: partial derivative with respect to stock price
        dS = S * 0.01  # 1% move
        price_up = self.binomial_option_price(S + dS, K, T, sigma, option_type, n)
        price_down = self.binomial_option_price(S - dS, K, T, sigma, option_type, n)
        delta = (price_up - price_down) / (2 * dS)
        
        # Gamma: second derivative with respect to stock price
        gamma = (price_up - 2 * price + price_down) / (dS ** 2)
        
        # Theta: partial derivative with respect to time
        dT = min(1/365, T/10)  # 1 day or 10% of time to expiration
        if T > dT:
            price_theta = self.binomial_option_price(S, K, T - dT, sigma, option_type, n)
            theta = (price_theta - price) / dT
        else:
            theta = 0.0
        
        # Vega: partial derivative with respect to volatility
        dsigma = 0.01  # 1% volatility change
        price_vega = self.binomial_option_price(S, K, T, sigma + dsigma, option_type, n)
        vega = (price_vega - price) / dsigma / 100  # Per 1% change in volatility
        
        return {
            'delta': round(delta, 4),
            'gamma': round(gamma, 4),
            'theta': round(theta / 365, 4),  # Convert to daily theta
            'vega': round(vega, 4)
        }
    
    def heston_option_price(self, S: float, K: float, T: float, sigma: float, 
                           option_type: str, v0: float = None, kappa: float = 2.0, 
                           theta: float = None, sigma_v: float = 0.3, rho: float = -0.7, 
                           n_paths: int = 50000) -> float:
        """
        Calculate option price using Heston stochastic volatility model via Monte Carlo.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            sigma (float): Initial volatility estimate (used for v0 and theta if not provided)
            option_type (str): 'call' or 'put'
            v0 (float): Initial variance (default: sigma^2)
            kappa (float): Mean reversion speed (default: 2.0)
            theta (float): Long-term variance (default: sigma^2)
            sigma_v (float): Volatility of volatility (default: 0.3)
            rho (float): Correlation between stock and volatility (default: -0.7)
            n_paths (int): Number of Monte Carlo paths (default: 50000)
        
        Returns:
            float: Heston option price
        """
        if T <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        # Set default parameters based on initial volatility
        if v0 is None:
            v0 = sigma ** 2
        if theta is None:
            theta = sigma ** 2
        
        # Ensure positive variance
        v0 = max(v0, 0.01)
        theta = max(theta, 0.01)
        
        # Time discretization
        n_steps = max(int(T * 252), 50)  # Daily steps, minimum 50
        dt = T / n_steps
        
        # Monte Carlo simulation
        np.random.seed(42)  # For reproducibility
        
        # Generate correlated random numbers
        Z1 = np.random.normal(0, 1, (n_paths, n_steps))
        Z2 = np.random.normal(0, 1, (n_paths, n_steps))
        W_S = Z1
        W_v = rho * Z1 + np.sqrt(1 - rho**2) * Z2
        
        # Initialize arrays
        S_paths = np.zeros((n_paths, n_steps + 1))
        v_paths = np.zeros((n_paths, n_steps + 1))
        
        S_paths[:, 0] = S
        v_paths[:, 0] = v0
        
        # Simulate paths using Euler scheme with Feller boundary condition
        for i in range(n_steps):
            # Ensure variance stays positive (Feller boundary condition)
            v_paths[:, i] = np.maximum(v_paths[:, i], 0.0001)
            
            # Update variance (CIR process)
            dv = kappa * (theta - v_paths[:, i]) * dt + sigma_v * np.sqrt(v_paths[:, i]) * np.sqrt(dt) * W_v[:, i]
            v_paths[:, i + 1] = v_paths[:, i] + dv
            
            # Update stock price
            dS = self.risk_free_rate * S_paths[:, i] * dt + np.sqrt(v_paths[:, i]) * S_paths[:, i] * np.sqrt(dt) * W_S[:, i]
            S_paths[:, i + 1] = S_paths[:, i] + dS
        
        # Calculate payoffs
        S_final = S_paths[:, -1]
        if option_type.lower() == 'call':
            payoffs = np.maximum(S_final - K, 0)
        else:  # put
            payoffs = np.maximum(K - S_final, 0)
        
        # Discount and take expectation
        option_price = np.exp(-self.risk_free_rate * T) * np.mean(payoffs)
        
        return float(option_price)
    
    def heston_greeks(self, S: float, K: float, T: float, sigma: float, 
                     option_type: str, v0: float = None, kappa: float = 2.0, 
                     theta: float = None, sigma_v: float = 0.3, rho: float = -0.7) -> Dict[str, float]:
        """
        Calculate Greeks using Heston model with finite differences.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration in years
            sigma (float): Initial volatility estimate
            option_type (str): 'call' or 'put'
            v0, kappa, theta, sigma_v, rho: Heston model parameters
        
        Returns:
            Dict[str, float]: Dictionary with delta, gamma, theta, vega
        """
        if T <= 0:
            # At expiration
            if option_type.lower() == 'call':
                delta = 1.0 if S > K else 0.0
            else:  # put
                delta = -1.0 if S < K else 0.0
            
            return {
                'delta': delta,
                'gamma': 0.0,
                'theta': 0.0,
                'vega': 0.0
            }
        
        # Base price
        price = self.heston_option_price(S, K, T, sigma, option_type, v0, kappa, theta, sigma_v, rho, n_paths=25000)
        
        # Delta: partial derivative with respect to stock price
        dS = S * 0.01  # 1% move
        price_up = self.heston_option_price(S + dS, K, T, sigma, option_type, v0, kappa, theta, sigma_v, rho, n_paths=25000)
        price_down = self.heston_option_price(S - dS, K, T, sigma, option_type, v0, kappa, theta, sigma_v, rho, n_paths=25000)
        delta = (price_up - price_down) / (2 * dS)
        
        # Gamma: second derivative with respect to stock price
        gamma = (price_up - 2 * price + price_down) / (dS ** 2)
        
        # Theta: partial derivative with respect to time
        dT = min(1/365, T/10)  # 1 day or 10% of time to expiration
        if T > dT:
            price_theta = self.heston_option_price(S, K, T - dT, sigma, option_type, v0, kappa, theta, sigma_v, rho, n_paths=25000)
            theta = (price_theta - price) / dT
        else:
            theta = 0.0
        
        # Vega: partial derivative with respect to initial volatility
        dsigma = 0.01  # 1% volatility change
        v0_new = max(0.0001, (sigma + dsigma) ** 2)
        theta_new = v0_new  # Keep theta = v0 for consistency
        price_vega = self.heston_option_price(S, K, T, sigma + dsigma, option_type, v0_new, kappa, theta_new, sigma_v, rho, n_paths=25000)
        vega = (price_vega - price) / dsigma / 100  # Per 1% change in volatility
        
        return {
            'delta': round(delta, 4),
            'gamma': round(gamma, 4),
            'theta': round(theta / 365, 4),  # Convert to daily theta
            'vega': round(vega, 4)
        }
    
    def calculate_fallback_greeks(self, option: Dict[str, Any], underlying_price: float) -> Dict[str, float]:
        """Calculate fallback Greeks when API data is unavailable."""
        strike = option['strike']
        is_call = option['option_type'] == 'call'
        
        # Calculate moneyness and distance from strike
        moneyness = underlying_price / strike
        distance = abs(underlying_price - strike) / strike
        
        # More realistic delta calculations based on how far ITM/OTM
        if is_call:
            if moneyness > 1.20:  # Deep ITM call
                delta = 0.95 + min(0.04, (moneyness - 1.20) * 0.1)
            elif moneyness > 1.05:  # ITM call
                delta = 0.60 + (moneyness - 1.05) * 2.0
            elif moneyness > 0.95:  # Near ATM call
                delta = 0.30 + (moneyness - 0.95) * 3.0
            elif moneyness > 0.80:  # OTM call
                delta = 0.05 + (moneyness - 0.80) * 1.67
            else:  # Deep OTM call
                delta = max(0.01, 0.05 * moneyness / 0.80)
        else:  # put
            if moneyness < 0.80:  # Deep ITM put
                delta = -0.95 - min(0.04, (0.80 - moneyness) * 0.1)
            elif moneyness < 0.95:  # ITM put
                delta = -0.60 - (0.95 - moneyness) * 2.0
            elif moneyness < 1.05:  # Near ATM put
                delta = -0.30 - (1.05 - moneyness) * 3.0
            elif moneyness < 1.20:  # OTM put
                delta = -0.05 - (1.20 - moneyness) * 1.67
            else:  # Deep OTM put
                delta = -max(0.01, 0.05 * (2.40 - moneyness) / 1.20)
        
        # Gamma is highest near ATM and decreases as we move away
        if distance < 0.05:  # Very close to ATM
            gamma = 0.015
        elif distance < 0.10:  # Close to ATM
            gamma = 0.012
        elif distance < 0.20:  # Moderately away from ATM
            gamma = 0.008
        elif distance < 0.50:  # Far from ATM
            gamma = 0.003
        else:  # Very far from ATM
            gamma = 0.001
        
        # Vega is also highest near ATM and decreases for deep ITM/OTM
        if distance < 0.05:  # Very close to ATM
            vega = 0.25
        elif distance < 0.10:  # Close to ATM
            vega = 0.20
        elif distance < 0.20:  # Moderately away from ATM
            vega = 0.12
        elif distance < 0.50:  # Far from ATM
            vega = 0.06
        else:  # Very far from ATM (like our 5700/5800 strikes)
            vega = 0.02
        
        # Theta (time decay) - more negative for ATM options
        if distance < 0.10:
            theta = -0.08
        elif distance < 0.30:
            theta = -0.05
        else:
            theta = -0.02
        
        return {
            'delta': round(delta, 4),
            'gamma': round(gamma, 4),
            'theta': round(theta, 4),
            'vega': round(vega, 4)
        } 