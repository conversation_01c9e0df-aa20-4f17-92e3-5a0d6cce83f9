#!/usr/bin/env python3
"""
Simple test to verify the cleanup fix works
"""

import time
from market_data import MarketData

def test_cleanup_fix():
    """Test that cleanup doesn't cause runtime errors."""
    print("🧪 Testing cleanup fix...")
    
    try:
        # Create market data instance
        market_data = MarketData()
        print("✅ MarketData created")
        
        # Enable real-time streaming (this will start background threads)
        print("🚀 Enabling real-time streaming...")
        market_data.enable_realtime_streaming(['SPX'])
        
        # Wait a moment
        print("⏳ Waiting 2 seconds...")
        time.sleep(2)
        
        # Stop streaming (this should not cause runtime errors)
        print("⏹️ Stopping real-time streaming...")
        market_data.stop_realtime_streaming()
        print("✅ Cleanup completed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing Real-Time Cleanup Fix")
    print("=" * 40)
    
    if test_cleanup_fix():
        print("🎉 Fix verified! Cleanup works correctly.")
    else:
        print("💥 Fix failed! Still has issues.") 