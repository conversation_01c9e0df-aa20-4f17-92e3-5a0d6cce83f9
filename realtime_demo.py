#!/usr/bin/env python3
"""
Real-Time Index Streaming Demo
Demonstrates real-time streaming functionality for index data.
"""

import time
import signal
import sys
from typing import Dict, Any
from constants import DEFAULT_INDEX_SYMBOL, REALTIME_PRICE_THRESHOLD
from market_data import MarketData
from pricing_engine import PricingEngine
from spread_analyzer import SpreadAnalyzer


class RealTimeAnalyzer:
    """Real-time analyzer that updates analysis as prices change."""
    
    def __init__(self, ticker: str = DEFAULT_INDEX_SYMBOL):
        self.ticker = ticker
        self.market_data = MarketData()
        self.pricing_engine = PricingEngine()
        self.spread_analyzer = SpreadAnalyzer(self.market_data, self.pricing_engine)
        self.last_analysis_price = None
        self.price_threshold = REALTIME_PRICE_THRESHOLD  # Re-analyze if price changes by this much
        
    def on_price_update(self, symbol: str, price: float, data: Dict[str, Any]):
        """Callback function for price updates."""
        if symbol == self.ticker:
            # Check if price change is significant enough to trigger re-analysis
            if (self.last_analysis_price is None or 
                abs(price - self.last_analysis_price) >= self.price_threshold):
                
                print(f"\n🔄 Price change of ${abs(price - (self.last_analysis_price or 0)):.2f} detected")
                print(f"🧮 Triggering new analysis at ${price:.2f}...")
                
                try:
                    # Quick put spread analysis
                    result = self.spread_analyzer.analyze_spread(
                        ticker=symbol,
                        current_price=price,
                        exp_month=7,
                        exp_year=2025,
                        option_type="put",
                        spread_width=100,
                        price_range=10
                    )
                    
                    self.display_quick_summary(result, price)
                    self.last_analysis_price = price
                    
                except Exception as e:
                    print(f"❌ Analysis error: {e}")
    
    def display_quick_summary(self, result: Dict[str, Any], current_price: float):
        """Display a quick summary of the analysis."""
        print(f"\n📊 SPX PUT SPREAD ANALYSIS @ ${current_price:.2f}")
        print("=" * 50)
        
        # Get the spread info
        spread_info = result.get('spread_info', {})
        market_analysis = result.get('market_analysis', {})
        
        long_strike = spread_info.get('long_strike', 0)
        short_strike = spread_info.get('short_strike', 0)
        net_debit = spread_info.get('net_debit', 0)
        max_profit = spread_info.get('max_profit', 0)
        breakeven = spread_info.get('breakeven', 0)
        
        print(f"🎯 Strategy: {long_strike:.0f}/{short_strike:.0f} Put Spread")
        print(f"💰 Net Debit: ${net_debit:.2f}")
        print(f"📈 Max Profit: ${max_profit:.2f}")
        print(f"⚖️  Breakeven: ${breakeven:.2f}")
        
        # Show theoretical vs market prices
        long_option = market_analysis.get('long_option', {})
        if long_option:
            market_price = long_option.get('market_price', 0)
            bs_price = long_option.get('black_scholes_price', 0)
            print(f"🏷️  Long Put: Market ${market_price:.2f} | BS ${bs_price:.2f}")
        
        print("-" * 50)
    
    def start_streaming(self):
        """Start real-time streaming with analysis."""
        print(f"🚀 Starting real-time analysis for {self.ticker}")
        print(f"🔔 Will re-analyze when price changes by ${self.price_threshold:.2f} or more")
        print("Press Ctrl+C to stop\n")
        
        # Add our callback to the market data
        self.market_data.add_price_update_callback(self.on_price_update)
        
        # Enable streaming
        self.market_data.enable_realtime_streaming([self.ticker])
        
        return self.market_data


def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully."""
    print('\n\n👋 Stopping real-time streaming...')
    sys.exit(0)


def main():
    """Main demo function."""
    print("🎯 SPX Real-Time Options Analysis Demo")
    print("=" * 50)
    
    # Set up signal handler for graceful exit
    signal.signal(signal.SIGINT, signal_handler)
    
    # Create analyzer
    analyzer = RealTimeAnalyzer("SPX")
    
    try:
        # Start streaming
        market_data = analyzer.start_streaming()
        
        # Keep running until interrupted
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print('\n\n👋 Stopping real-time streaming...')
    except Exception as e:
        print(f'❌ Error: {e}')
    finally:
        if 'market_data' in locals():
            try:
                market_data.stop_realtime_streaming()
            except Exception as e:
                print(f"Warning: Cleanup error: {e}")


def quick_realtime_demo():
    """Quick demo showing basic real-time price streaming."""
    print("🔴 SPX Real-Time Price Demo")
    print("=" * 30)
    print("Press Ctrl+C to stop\n")
    
    # Create market data instance
    market_data = MarketData()
    
    try:
        # Enable streaming for SPX
        market_data.enable_realtime_streaming(['SPX'])
        
        # Let it run for a bit
        print("⏳ Streaming live prices...")
        
        # Keep running until interrupted
        while True:
            time.sleep(1)
            
            # Optionally show latest cached price every 10 seconds
            if int(time.time()) % 10 == 0:
                latest = market_data.get_realtime_price('SPX')
                if latest:
                    print(f"💾 Cached price: ${latest:.2f}")
                    
    except KeyboardInterrupt:
        print('\n\n👋 Demo complete!')
    finally:
        try:
            market_data.stop_realtime_streaming()
        except Exception as e:
            print(f"Warning: Cleanup error: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Real-time SPX streaming demo')
    parser.add_argument('--analysis', '-a', action='store_true', 
                       help='Run full analysis demo (default: price streaming only)')
    parser.add_argument('--ticker', '-t', default='SPX',
                       help='Index ticker to stream (default: SPX)')
    
    args = parser.parse_args()
    
    if args.analysis:
        main()
    else:
        quick_realtime_demo() 