# Real-Time Index Streaming Guide

This guide explains how to use the new real-time streaming functionality for index data in the Options Spread Analyzer.

## 🚀 Quick Start

### Basic Real-Time Price Streaming
```bash
# Stream live SPX prices
python realtime_demo.py

# Stream with automatic options analysis
python realtime_demo.py --analysis
```

### Integration with Main Analysis
```bash
# Enable real-time for regular analysis
python main.py --realtime

# Real-time with custom parameters
python main.py --realtime -t SPX -m 7 -y 2025 --spread-width 50
```

## 📋 API Reference

### MarketData Class - New Methods

#### `enable_realtime_streaming(symbols, on_update=None)`
Enables real-time WebSocket streaming for specified index symbols.

**Parameters:**
- `symbols` (List[str]): Index symbols to stream (default: ['SPX'])
- `on_update` (Callable): Optional callback for price updates

**Example:**
```python
from market_data import MarketData

market_data = MarketData()
market_data.enable_realtime_streaming(['SPX'])
```

#### `get_realtime_price(symbol)`
Gets the latest cached real-time price for a symbol.

**Parameters:**
- `symbol` (str): Index symbol

**Returns:**
- `float` or `None`: Latest price if available

**Example:**
```python
price = market_data.get_realtime_price('SPX')
if price:
    print(f"Current SPX: ${price:.2f}")
```

#### `add_price_update_callback(callback)`
Adds a callback function to be called on every price update.

**Parameters:**
- `callback` (Callable): Function that accepts (symbol, price, data)

**Example:**
```python
def my_callback(symbol, price, data):
    print(f"New {symbol} price: ${price:.2f}")

market_data.add_price_update_callback(my_callback)
```

#### `get_current_price(symbol, is_index=True, use_realtime=True)`
Enhanced version that can use real-time prices when available.

**New Parameter:**
- `use_realtime` (bool): Whether to prefer real-time over API calls

## 🔧 Implementation Examples

### Basic Real-Time Monitoring
```python
from market_data import MarketData
import time

# Create market data instance
market_data = MarketData()

# Define callback for price updates
def on_price_change(symbol, price, data):
    timestamp = data.get('t', 0)
    print(f"📊 {symbol}: ${price:.2f} at {timestamp}")

# Enable streaming with callback
market_data.enable_realtime_streaming(['SPX'], on_price_change)

# Monitor for 60 seconds
time.sleep(60)

# Clean up
market_data.stop_realtime_streaming()
```

### Real-Time Analysis Trigger
```python
from market_data import MarketData
from spread_analyzer import SpreadAnalyzer
from pricing_engine import PricingEngine

class RealTimeAnalyzer:
    def __init__(self):
        self.market_data = MarketData()
        self.pricing_engine = PricingEngine()
        self.analyzer = SpreadAnalyzer(self.market_data, self.pricing_engine)
        self.last_price = None
        
    def on_price_update(self, symbol, price, data):
        # Re-analyze if price changed significantly
        if self.last_price is None or abs(price - self.last_price) >= 5.0:
            print(f"🔄 Significant price change detected: ${price:.2f}")
            self.run_analysis(symbol, price)
            self.last_price = price
    
    def run_analysis(self, symbol, price):
        result = self.analyzer.analyze_spread(
            ticker=symbol,
            current_price=price,
            exp_month=7,
            exp_year=2025,
            option_type="put"
        )
        # Process results...
        
    def start(self):
        self.market_data.add_price_update_callback(self.on_price_update)
        self.market_data.enable_realtime_streaming(['SPX'])

# Usage
analyzer = RealTimeAnalyzer()
analyzer.start()
```

### Portfolio Monitoring
```python
from market_data import MarketData

class PortfolioMonitor:
    def __init__(self, symbols):
        self.symbols = symbols
        self.market_data = MarketData()
        self.prices = {}
        
    def on_price_update(self, symbol, price, data):
        old_price = self.prices.get(symbol, price)
        self.prices[symbol] = price
        
        change = price - old_price
        change_pct = (change / old_price) * 100 if old_price else 0
        
        print(f"📈 {symbol}: ${price:.2f} ({change:+.2f}, {change_pct:+.2f}%)")
        
    def start_monitoring(self):
        self.market_data.add_price_update_callback(self.on_price_update)
        self.market_data.enable_realtime_streaming(self.symbols)
        
# Monitor multiple indices
monitor = PortfolioMonitor(['SPX', 'NDX', 'RUT'])
monitor.start_monitoring()
```

## ⚙️ Configuration

### Environment Setup
Make sure you have your Polygon API key configured:
```bash
# .env file
POLYGON_API_KEY=your_actual_api_key_here
```

### Dependencies
The real-time functionality requires the `websockets` library:
```bash
pip install websockets>=11.0.0
```

Or install all dependencies:
```bash
pip install -r requirements.txt
```

## 🚨 Error Handling

The real-time system includes robust error handling:

1. **WebSocket Connection Failures**: Automatically attempts to reconnect
2. **API Fallback**: Falls back to regular API calls if streaming fails
3. **Market Hours**: Gracefully handles when markets are closed
4. **Network Issues**: Automatic reconnection with exponential backoff
5. **Clean Shutdown**: Properly handles cleanup when stopping streams
6. **Thread Safety**: Safe cleanup of background threads and connections

## 🕒 Market Hours Considerations

- Real-time streaming works best during market hours (9:30 AM - 4:00 PM ET)
- Outside market hours, updates may be infrequent or unavailable
- The system will fall back to API calls when real-time data is stale

## 🔧 Troubleshooting

### No Real-Time Updates
1. Check your Polygon API key has streaming permissions
2. Verify you're testing during market hours
3. Check network connectivity
4. Ensure WebSocket ports aren't blocked

### High CPU Usage
1. Limit the number of symbols you're streaming
2. Reduce callback frequency by adding price thresholds
3. Use the built-in caching instead of processing every update

### Memory Usage
1. The system automatically manages connection cleanup
2. Always call `stop_realtime_streaming()` when done
3. Price cache is automatically limited to prevent memory leaks

## 📊 Performance Tips

1. **Batch Processing**: Process multiple updates in batches rather than one by one
2. **Price Thresholds**: Only trigger analysis on significant price changes
3. **Symbol Limits**: Stream only the symbols you actually need
4. **Callback Efficiency**: Keep callback functions lightweight and fast

## 🔄 Migration from API-Only

If you're upgrading from the API-only version:

1. **Backwards Compatible**: All existing code continues to work
2. **Opt-In**: Real-time is disabled by default
3. **Graceful Fallback**: API calls work if streaming fails
4. **Same Interface**: `get_current_price()` works the same way

## 📝 Examples Repository

See the `realtime_demo.py` and `test_realtime.py` files for complete working examples of:
- Basic price streaming
- Analysis triggers
- Error handling
- Performance optimization 