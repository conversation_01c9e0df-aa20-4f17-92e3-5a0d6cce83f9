#!/usr/bin/env python3
"""
Market Data Module
Handles all API calls and market data retrieval from Polygon.io
Includes real-time streaming capabilities for indices
"""

import os
import requests
import urllib.parse
from typing import Dict, List, Any, Callable, Optional
from datetime import datetime, date
import calendar
import logging
import asyncio
import websockets
import json
import threading
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class RealTimeIndexStreamer:
    """Real-time streaming for index data using Polygon WebSocket API."""
    
    def __init__(self, api_key: str, on_price_update: Callable[[str, float, dict], None]):
        """
        Initialize real-time streamer.
        
        Args:
            api_key (str): Polygon API key
            on_price_update (Callable): Callback function for price updates
        """
        self.api_key = api_key
        self.on_price_update = on_price_update
        self.websocket = None
        self.is_connected = False
        self.subscribed_symbols = set()
        self.latest_prices = {}
        self.connection_thread = None
        self.should_reconnect = True
        
    async def connect(self):
        """Connect to Polygon WebSocket API."""
        uri = f"wss://socket.polygon.io/indices"
        try:
            self.websocket = await websockets.connect(uri)
            self.is_connected = True
            logging.info("🔗 Connected to Polygon WebSocket for indices")
            
            # Authenticate
            auth_msg = {
                "action": "auth",
                "params": self.api_key
            }
            await self.websocket.send(json.dumps(auth_msg))
            
            # Listen for messages
            async for message in self.websocket:
                if not self.should_reconnect:
                    # Shutdown signal received
                    break
                await self.handle_message(message)
                
        except websockets.exceptions.ConnectionClosed:
            logging.info("WebSocket connection closed")
            self.is_connected = False
        except Exception as e:
            logging.error(f"WebSocket connection error: {e}")
            self.is_connected = False
            if self.should_reconnect:
                logging.info("Attempting to reconnect in 5 seconds...")
                await asyncio.sleep(5)
                if self.should_reconnect:  # Check again after sleep
                    await self.connect()
        finally:
            # Ensure websocket is closed
            if self.websocket and not self.websocket.closed:
                await self.websocket.close()
            self.is_connected = False
    
    async def handle_message(self, message: str):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(message)
            
            if isinstance(data, list):
                for item in data:
                    await self.process_index_update(item)
            else:
                await self.process_index_update(data)
                
        except Exception as e:
            logging.error(f"Error processing WebSocket message: {e}")
    
    async def process_index_update(self, data: dict):
        """Process index price update."""
        try:
            if data.get('ev') == 'V':  # Index value update
                symbol = data.get('T', '').replace('I:', '')
                value = float(data.get('val', 0))
                timestamp = data.get('t', 0)
                
                if symbol in self.subscribed_symbols:
                    self.latest_prices[symbol] = {
                        'price': value,
                        'timestamp': timestamp,
                        'updated': datetime.now()
                    }
                    
                    # Call the callback function
                    self.on_price_update(symbol, value, data)
                    
        except Exception as e:
            logging.error(f"Error processing index update: {e}")
    
    async def subscribe_to_index(self, symbol: str):
        """Subscribe to real-time updates for an index."""
        if self.websocket and self.is_connected:
            subscribe_msg = {
                "action": "subscribe",
                "params": f"V.I:{symbol.upper()}"
            }
            await self.websocket.send(json.dumps(subscribe_msg))
            self.subscribed_symbols.add(symbol.upper())
            logging.info(f"📊 Subscribed to real-time updates for {symbol}")
    
    def start_streaming(self, symbols: List[str]):
        """Start streaming in a separate thread."""
        def run_stream():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def stream():
                await self.connect()
                for symbol in symbols:
                    await self.subscribe_to_index(symbol)
            
            try:
                loop.run_until_complete(stream())
            except Exception as e:
                logging.error(f"Streaming error: {e}")
            finally:
                loop.close()
        
        self.connection_thread = threading.Thread(target=run_stream, daemon=True)
        self.connection_thread.start()
        
        # Wait a moment for connection to establish
        time.sleep(2)
    
    def stop_streaming(self):
        """Stop the streaming connection."""
        self.should_reconnect = False
        self.is_connected = False
        
        # Wait for connection thread to finish (it will close the websocket automatically)
        if self.connection_thread and self.connection_thread.is_alive():
            # The thread will exit naturally when should_reconnect is False
            self.connection_thread.join(timeout=3)
        
        # Clear websocket reference
        self.websocket = None
    
    def get_latest_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get the latest cached price for a symbol."""
        return self.latest_prices.get(symbol.upper())


class MarketData:
    """Handle market data retrieval from Polygon API with real-time streaming."""
    
    def __init__(self, api_key: str = None):
        """
        Initialize MarketData with API key and optional real-time streaming.
        
        Args:
            api_key (str): Polygon API key. If None, loads from environment.
        """
        self.api_key = api_key or self._get_polygon_api_key()
        self.realtime_streamer = None
        self.price_update_callbacks = []
        self.streaming_enabled = False
    
    def _get_polygon_api_key(self) -> str:
        """Get Polygon API key from environment variables."""
        api_key = os.getenv('POLYGON_API_KEY')
        if not api_key or api_key == 'your_polygon_api_key_here':
            raise ValueError("Please set your Polygon API key in the .env file")
        return api_key
    
    def enable_realtime_streaming(self, symbols: List[str] = None, 
                                 on_update: Callable[[str, float, dict], None] = None):
        """
        Enable real-time streaming for specified symbols.
        
        Args:
            symbols (List[str]): List of index symbols to stream (default: ['SPX'])
            on_update (Callable): Optional callback for price updates
        """
        if symbols is None:
            symbols = ['SPX']
        
        def default_update_callback(symbol: str, price: float, data: dict):
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"🔴 LIVE {timestamp} | {symbol}: ${price:.2f}")
            
            # Call any registered callbacks
            for callback in self.price_update_callbacks:
                try:
                    callback(symbol, price, data)
                except Exception as e:
                    logging.error(f"Error in price update callback: {e}")
        
        update_callback = on_update or default_update_callback
        
        self.realtime_streamer = RealTimeIndexStreamer(self.api_key, update_callback)
        self.realtime_streamer.start_streaming(symbols)
        self.streaming_enabled = True
        
        print(f"🚀 Real-time streaming enabled for {', '.join(symbols)}")
        print("💡 Use get_realtime_price() to get live prices or stop_realtime_streaming() to disable")
    
    def add_price_update_callback(self, callback: Callable[[str, float, dict], None]):
        """Add a callback function to be called on price updates."""
        self.price_update_callbacks.append(callback)
    
    def get_realtime_price(self, symbol: str) -> Optional[float]:
        """
        Get the latest real-time price for a symbol.
        
        Args:
            symbol (str): Index symbol
            
        Returns:
            Optional[float]: Latest price if available, None otherwise
        """
        if not self.streaming_enabled or not self.realtime_streamer:
            return None
        
        price_data = self.realtime_streamer.get_latest_price(symbol)
        if price_data:
            return price_data['price']
        return None
    
    def stop_realtime_streaming(self):
        """Stop real-time streaming."""
        if self.realtime_streamer:
            self.realtime_streamer.stop_streaming()
            self.streaming_enabled = False
            print("⏹️ Real-time streaming stopped")
    
    def get_current_price(self, symbol: str, is_index: bool = True, use_realtime: bool = True) -> float:
        """
        Get current stock/index price from Polygon API or real-time stream.
        
        Args:
            symbol (str): Stock/index symbol
            is_index (bool): Whether this is an index (adds i: prefix) or stock
            use_realtime (bool): Whether to use real-time price if available
            
        Returns:
            float: Current price
            
        Raises:
            Exception: If API fails to retrieve price
        """
        # Try real-time price first if enabled and requested
        if use_realtime and self.streaming_enabled and is_index:
            realtime_price = self.get_realtime_price(symbol)
            if realtime_price is not None:
                print(f"📊 Using real-time price: ${realtime_price:.2f}")
                return realtime_price
        
        # Fall back to API call
        if is_index:
            # For indices, use the snapshot endpoint with URL-encoded ticker
            api_symbol = f"I:{symbol.upper()}"
            encoded_ticker = urllib.parse.quote(api_symbol)
            url = f"https://api.polygon.io/v3/snapshot/indices?ticker={encoded_ticker}&order=asc&limit=10&sort=ticker"
        else:
            # For stocks, use the regular ticker and stocks endpoint
            api_symbol = symbol.upper()
            url = f"https://api.polygon.io/v2/aggs/ticker/{api_symbol}/prev"
        
        params = {
            'apikey' if not is_index else 'apiKey': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if is_index:
                # For indices snapshot endpoint
                if data.get('status') == 'OK' and data.get('results') and len(data['results']) > 0:
                    result = data['results'][0]
                    # Try value field first, then session.close as fallback
                    if 'value' in result and result['value'] is not None:
                        price = float(result['value'])
                        print(f"📊 Retrieved API price: ${price:.2f}")
                        return price
                    elif 'session' in result and result['session'].get('close') is not None:
                        price = float(result['session']['close'])
                        print(f"📊 Retrieved API price (session close): ${price:.2f}")
                        return price
                    else:
                        raise Exception(f"Index data found but no price value for {symbol}")
                else:
                    raise Exception(f"Could not retrieve index price for {symbol} from API")
            else:
                # For stocks endpoint
                if data['status'] == 'OK' and data['resultsCount'] > 0:
                    price = float(data['results'][0]['c'])  # Close price
                    print(f"📊 Retrieved API price: ${price:.2f}")
                    return price
                else:
                    raise Exception(f"Could not retrieve stock price for {symbol} from API")
                    
        except Exception as e:
            raise Exception(f"API error retrieving price for {symbol}: {str(e)}")
    
    def get_third_friday(self, year: int, month: int) -> str:
        """
        Calculate the 3rd Friday of a given month and year (options expiration date).
        
        Args:
            year (int): Year (e.g., 2024)
            month (int): Month (1-12)
        
        Returns:
            str: Date in YYYY-MM-DD format
        """
        # Get the first day of the month
        first_day = date(year, month, 1)
        
        # Find the first Friday
        first_friday = 1
        while date(year, month, first_friday).weekday() != 4:  # 4 = Friday
            first_friday += 1
        
        # Third Friday is first Friday + 14 days
        third_friday = first_friday + 14
        
        # Make sure the date is valid for the month
        last_day = calendar.monthrange(year, month)[1]
        if third_friday > last_day:
            raise ValueError(f"No valid 3rd Friday in {year}-{month:02d}")
        
        expiration_date = date(year, month, third_friday)
        return expiration_date.strftime('%Y-%m-%d')
    
    def retrieve_options(self, symbol: str, strike: float, exp_month: int, exp_year: int) -> List[Dict[str, Any]]:
        """
        Retrieve options data for a given symbol and strike using Polygon API.
        Options will expire on the 3rd Friday of the specified month/year.
        
        Args:
            symbol (str): Stock symbol to retrieve options for
            strike (float): Strike price for the options
            exp_month (int): Expiration month (1-12)
            exp_year (int): Expiration year (e.g., 2024)
        
        Returns:
            List[Dict[str, Any]]: List of options data
        """
        # Calculate the 3rd Friday expiration date
        try:
            expiration_date = self.get_third_friday(exp_year, exp_month)
            logging.info(f"Retrieving options for symbol: {symbol}, strike: ${strike}")
            logging.info(f"Expiration: 3rd Friday of {exp_year}-{exp_month:02d} ({expiration_date})")
        except ValueError as e:
            logging.error(f"Invalid expiration month/year: {e}")
            return []
        
        return self.retrieve_options_by_date(symbol, strike, expiration_date)
    
    def retrieve_options_by_date(self, symbol: str, strike: float, expiration_date: str) -> List[Dict[str, Any]]:
        """
        Retrieve options data for a specific expiration date.
        This bypasses the 3rd Friday calculation and uses the exact date provided.
        """
        # Search for option contracts using the API
        contracts_url = "https://api.polygon.io/v3/reference/options/contracts"
        contracts_params = {
            'underlying_ticker': symbol,
            'expiration_date': expiration_date,
            'strike_price': strike,
            'limit': 1000,
            'apikey': self.api_key
        }
        
        try:
            contracts_response = requests.get(contracts_url, params=contracts_params)
            contracts_response.raise_for_status()
            contracts_data = contracts_response.json()
            
            if contracts_data.get('status') != 'OK' or not contracts_data.get('results'):
                # Parse date for fallback
                exp_parts = expiration_date.split('-')
                exp_year, exp_month = int(exp_parts[0]), int(exp_parts[1])
                return self._create_fallback_options(symbol, strike, expiration_date, exp_month, exp_year)
            
            options_data = []
            contracts = contracts_data['results']
            
            # Process each contract found
            for contract in contracts:
                option_symbol = contract['ticker']
                option_type = 'call' if contract['contract_type'] == 'call' else 'put'
                
                try:
                    # Get current quote for this option
                    quote_url = f"https://api.polygon.io/v3/quotes/{option_symbol}"
                    quote_params = {
                        'apikey': self.api_key,
                        'limit': 1
                    }
                    
                    quote_response = requests.get(quote_url, params=quote_params)
                    quote_response.raise_for_status()
                    quote_data = quote_response.json()
                    
                    # Create option data entry
                    exp_parts = expiration_date.split('-')
                    option_data = {
                        'symbol': option_symbol,
                        'strike': float(contract['strike_price']),
                        'expiration': expiration_date,
                        'exp_month': int(exp_parts[1]),
                        'exp_year': int(exp_parts[0]),
                        'option_type': option_type,
                        'contract_type': contract['contract_type'],
                        'shares_per_contract': contract.get('shares_per_contract', 100),
                        'ticker': contract.get('underlying_ticker'),
                        'bid': 0,
                        'ask': 0,
                        'last': 0,
                        'volume': 0,
                        'timestamp': 0
                    }
                    
                    # Add quote data if available
                    if quote_data.get('status') == 'OK' and quote_data.get('results'):
                        quote_result = quote_data['results'][0]
                        option_data.update({
                            'bid': float(quote_result.get('bid_price', 0)),
                            'ask': float(quote_result.get('ask_price', 0)),
                            'last': float(quote_result.get('last_price', 0)),
                            'bid_size': int(quote_result.get('bid_size', 0)),
                            'ask_size': int(quote_result.get('ask_size', 0)),
                            'timestamp': quote_result.get('sip_timestamp', 0)
                        })
                    
                    options_data.append(option_data)
                    
                except Exception:
                    # Add the contract without quote data
                    exp_parts = expiration_date.split('-')
                    option_data = {
                        'symbol': option_symbol,
                        'strike': float(contract['strike_price']),
                        'expiration': expiration_date,
                        'exp_month': int(exp_parts[1]),
                        'exp_year': int(exp_parts[0]),
                        'option_type': option_type,
                        'contract_type': contract['contract_type'],
                        'shares_per_contract': contract.get('shares_per_contract', 100),
                        'ticker': contract.get('underlying_ticker'),
                        'bid': 0,
                        'ask': 0,
                        'last': 0,
                        'volume': 0
                    }
                    options_data.append(option_data)
            
            return options_data
            
        except Exception:
            exp_parts = expiration_date.split('-')
            exp_year, exp_month = int(exp_parts[0]), int(exp_parts[1])
            return self._create_fallback_options(symbol, strike, expiration_date, exp_month, exp_year)
    
    def _create_fallback_options(self, symbol: str, strike: float, expiration_date: str, 
                              exp_month: int, exp_year: int) -> List[Dict[str, Any]]:
        """Create fallback option data when API search fails."""
        
        # Create realistic fallback symbols
        strike_formatted = f"{int(strike * 1000):08d}"
        call_symbol = f"O:{symbol}{expiration_date.replace('-', '')}C{strike_formatted}"
        put_symbol = f"O:{symbol}{expiration_date.replace('-', '')}P{strike_formatted}"
        
        fallback_data = []
        for option_symbol, option_type in [(call_symbol, 'call'), (put_symbol, 'put')]:
            option_data = {
                'symbol': option_symbol,
                'strike': strike,
                'expiration': expiration_date,
                'exp_month': exp_month,
                'exp_year': exp_year,
                'option_type': option_type,
                'bid': 2.10 if option_type == 'put' else 5.20,
                'ask': 2.30 if option_type == 'put' else 5.40,
                'last': 2.20 if option_type == 'put' else 5.30,
                'volume': 890 if option_type == 'put' else 1250
            }
            fallback_data.append(option_data)
        
        return fallback_data 