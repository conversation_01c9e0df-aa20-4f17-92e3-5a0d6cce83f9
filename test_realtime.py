#!/usr/bin/env python3
"""
Test script for real-time functionality
Tests both API fallback and real-time streaming capabilities
"""

import time
import sys
from market_data import MarketData


def test_api_fallback():
    """Test that API calls still work without real-time enabled."""
    print("🧪 Testing API fallback functionality...")
    
    try:
        market_data = MarketData()
        
        # Test SPX price retrieval
        price = market_data.get_current_price("SPX", is_index=True, use_realtime=False)
        print(f"✅ SPX API price: ${price:.2f}")
        
        # Test that real-time returns None when not enabled
        rt_price = market_data.get_realtime_price("SPX")
        assert rt_price is None, "Real-time price should be None when not enabled"
        print("✅ Real-time correctly returns None when disabled")
        
        return True
        
    except Exception as e:
        print(f"❌ API fallback test failed: {e}")
        return False


def test_realtime_connection():
    """Test real-time streaming connection."""
    print("\n🧪 Testing real-time streaming connection...")
    
    try:
        market_data = MarketData()
        
        # Counter for price updates
        update_count = 0
        
        def count_updates(symbol, price, data):
            nonlocal update_count
            update_count += 1
            print(f"📊 Update #{update_count}: {symbol} = ${price:.2f}")
        
        # Add callback and enable streaming
        market_data.add_price_update_callback(count_updates)
        market_data.enable_realtime_streaming(['SPX'])
        
        print("⏳ Waiting for real-time updates (15 seconds)...")
        start_time = time.time()
        
        while time.time() - start_time < 15:
            time.sleep(1)
            
            # Check if we got any updates
            if update_count > 0:
                rt_price = market_data.get_realtime_price('SPX')
                if rt_price:
                    print(f"✅ Cached real-time price: ${rt_price:.2f}")
        
        # Clean up
        try:
            market_data.stop_realtime_streaming()
        except Exception as e:
            print(f"Warning: Cleanup error: {e}")
        
        if update_count > 0:
            print(f"✅ Real-time streaming test passed! Received {update_count} updates")
            return True
        else:
            print("⚠️ No real-time updates received (may be market hours or connectivity issue)")
            return True  # Don't fail the test for market hours
            
    except Exception as e:
        print(f"❌ Real-time streaming test failed: {e}")
        return False


def test_price_retrieval_with_realtime():
    """Test get_current_price with real-time enabled."""
    print("\n🧪 Testing price retrieval with real-time enabled...")
    
    try:
        market_data = MarketData()
        
        # Enable real-time
        market_data.enable_realtime_streaming(['SPX'])
        
        # Wait a moment for connection
        time.sleep(3)
        
        # Get price with real-time preference
        price = market_data.get_current_price("SPX", is_index=True, use_realtime=True)
        print(f"✅ Price retrieved: ${price:.2f}")
        
        # Clean up
        try:
            market_data.stop_realtime_streaming()
        except Exception as e:
            print(f"Warning: Cleanup error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Price retrieval test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Real-Time Functionality Tests")
    print("=" * 50)
    
    tests = [
        ("API Fallback", test_api_fallback),
        ("Real-Time Connection", test_realtime_connection),
        ("Price Retrieval with Real-Time", test_price_retrieval_with_realtime)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("💡 Some tests failed - check your API key and network connection")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 