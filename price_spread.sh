#!/bin/bash

# SPX Options Spread Analysis with configurable parameters
# Usage: ./price_spread.sh [width] [range] [price]
# Defaults: width=100, range=20, price=API
# Runs both PUT and CALL spread analysis in one consolidated report

# Set parameters with defaults
WIDTH=${1:-100}        # Default: 100
RANGE=${2:-20}         # Default: 20
PRICE=${3}             # Default: use API (no default value)

echo "🔧 Running SPX consolidated spread analysis with:"
echo "   Spread Width: $WIDTH points"
echo "   Price Range: ±$RANGE points"
if [ -n "$PRICE" ]; then
    echo "   Manual Price: $PRICE"
    PRICE_PARAM="--price $PRICE"
else
    echo "   Price Source: API"
    PRICE_PARAM=""
fi
echo "   Analysis: Both PUT and CALL spreads"
echo ""

.venv/bin/python main.py --ticker SPX --month 7 --year 2025 --spread-width $WIDTH --price-range $RANGE $PRICE_PARAM  --realtime

# Open the HTML report in default browser
open spread_analysis.html 