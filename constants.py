#!/usr/bin/env python3
"""
Constants Module
Centralized constants for the options trading system.
"""

# API Configuration
POLYGON_BASE_URL = "https://api.polygon.io"
POLYGON_WEBSOCKET_URL = "wss://socket.polygon.io"

# API Endpoints
POLYGON_SNAPSHOT_STOCKS = "/v2/snapshot/locale/us/markets/stocks/tickers"
POLYGON_SNAPSHOT_INDICES = "/v3/snapshot/indices"
POLYGON_AGGREGATES = "/v2/aggs/ticker"
POLYGON_OPTIONS_CONTRACTS = "/v3/reference/options/contracts"
POLYGON_OPTIONS_SNAPSHOT = "/v3/snapshot/options"
POLYGON_PREV_CLOSE = "/v2/aggs/ticker/{symbol}/prev"

# Default Values
DEFAULT_RISK_FREE_RATE = 0.05
DEFAULT_VOLATILITY = 0.20
DEFAULT_SPREAD_WIDTH = 100
DEFAULT_PRICE_RANGE = 20
DEFAULT_BINOMIAL_STEPS = 100
DEFAULT_HESTON_PATHS = 50000

# VRP Analysis Constants
VRP_STRONG_SELL_THRESHOLD = 0.08  # 8%
VRP_SELL_THRESHOLD = 0.04         # 4%
VRP_BUY_THRESHOLD = -0.04         # -4%
VRP_STRONG_BUY_THRESHOLD = -0.08  # -8%

# VRP Percentile Ranges
VRP_PERCENTILE_VERY_LOW = 5.0
VRP_PERCENTILE_LOW = 20.0
VRP_PERCENTILE_NEUTRAL_LOW = 40.0
VRP_PERCENTILE_NEUTRAL_HIGH = 60.0
VRP_PERCENTILE_HIGH = 80.0
VRP_PERCENTILE_VERY_HIGH = 95.0

# VRP Confidence Levels
VRP_HIGH_CONFIDENCE = 0.9
VRP_MEDIUM_CONFIDENCE = 0.7
VRP_LOW_CONFIDENCE = 0.5

# Time Periods
DAYS_IN_YEAR = 365.0
TRADING_DAYS_IN_YEAR = 252
VRP_LOOKBACK_DAYS = 252
VRP_SHORT_PERIOD = 30
VRP_LONG_PERIOD = 60
HISTORICAL_BUFFER_DAYS = 30

# Default Symbols
DEFAULT_INDEX_SYMBOL = "SPX"
DEFAULT_STOCK_SYMBOL = "AAPL"

# Date Formats
DATE_FORMAT_API = "%Y-%m-%d"
DATE_FORMAT_OPTION = "%y%m%d"

# API Parameters
API_PARAM_ADJUSTED = "true"
API_PARAM_SORT_ASC = "asc"
API_PARAM_LIMIT_DEFAULT = 1000
API_PARAM_LIMIT_MAX = 50000

# Real-time Streaming
REALTIME_PRICE_THRESHOLD = 1.0  # Re-analyze if price changes by this much
REALTIME_CONNECTION_TIMEOUT = 15  # seconds
REALTIME_RECONNECT_DELAY = 5  # seconds

# Option Pricing
VOLATILITY_ESTIMATION_TOLERANCE = 1e-6
VOLATILITY_ESTIMATION_MAX_ITERATIONS = 100
VOLATILITY_STEP_SIZE = 0.01  # 1% for vega calculation

# Heston Model Parameters
HESTON_DEFAULT_KAPPA = 2.0
HESTON_DEFAULT_SIGMA_V = 0.3
HESTON_DEFAULT_RHO = -0.7

# Display and Formatting
DECIMAL_PLACES_PRICE = 2
DECIMAL_PLACES_GREEKS = 4
DECIMAL_PLACES_PERCENTAGE = 1
DECIMAL_PLACES_VOLATILITY = 4

# File Extensions
HTML_EXTENSION = ".html"
LOG_EXTENSION = ".log"

# Signal Strength Values
SIGNAL_STRONG_SELL = "Strong Sell"
SIGNAL_SELL = "Sell"
SIGNAL_NEUTRAL = "Neutral"
SIGNAL_BUY = "Buy"
SIGNAL_STRONG_BUY = "Strong Buy"
SIGNAL_ERROR = "Error"

# Strategy Recommendations
STRATEGY_SELL_PREMIUM = "Sell straddles/strangles, iron condors, covered calls"
STRATEGY_CREDIT_SPREADS = "Sell premium strategies, credit spreads"
STRATEGY_DELTA_NEUTRAL = "Delta-neutral strategies, wait for better opportunities"
STRATEGY_DEBIT_SPREADS = "Buy options, debit spreads, protective puts"
STRATEGY_BUY_VOLATILITY = "Buy straddles/strangles, long volatility plays"
STRATEGY_ERROR = "Cannot analyze - no historical price data available"

# Index Prefixes
INDEX_PREFIX = "I:"
OPTION_PREFIX = "O:"

# Environment Variable Names
ENV_POLYGON_API_KEY = "POLYGON_API_KEY"
ENV_OPENAI_API_KEY = "OPEN_API_KEY"

# Default API Key Placeholder
DEFAULT_API_KEY_PLACEHOLDER = "your_polygon_api_key_here"

# WebSocket Message Types
WS_AUTH_MESSAGE = "auth"
WS_SUBSCRIBE_MESSAGE = "subscribe"

# Market Data Types
MARKET_DATA_STOCKS = "stocks"
MARKET_DATA_INDICES = "indices"
MARKET_DATA_OPTIONS = "options"

# Option Types
OPTION_TYPE_CALL = "call"
OPTION_TYPE_PUT = "put"
OPTION_TYPE_CALL_UPPER = "C"
OPTION_TYPE_PUT_UPPER = "P"

# Test Constants
TEST_SYMBOL_SPX = "SPX"
TEST_SYMBOL_AAPL = "AAPL"
TEST_PRICE_SPX = 5800.0
TEST_PRICE_AAPL = 175.0
TEST_MONTH = 7
TEST_YEAR = 2025
TEST_HISTORICAL_DAYS = 30
TEST_WAIT_TIME = 2  # seconds
TEST_REALTIME_WAIT = 15  # seconds

# Fallback Data
FALLBACK_PUT_BID = 2.10
FALLBACK_PUT_ASK = 2.30
FALLBACK_PUT_LAST = 2.20
FALLBACK_PUT_VOLUME = 890
FALLBACK_CALL_BID = 5.20
FALLBACK_CALL_ASK = 5.40
FALLBACK_CALL_LAST = 5.30
FALLBACK_CALL_VOLUME = 1250
