#!/usr/bin/env python3
"""
Report Generator Module
Handles HTML report generation and output formatting
"""

from typing import Dict, List, Any
from datetime import datetime
from pricing_engine import PricingEngine


class ReportGenerator:
    """Handle HTML report generation and output formatting."""
    
    def __init__(self):
        """Initialize ReportGenerator."""
        self.pricing_engine = PricingEngine()
    
    def get_price_classes(self, market: float, bs: float, binomial: float, heston: float) -> Dict[str, str]:
        """Return CSS classes for highlighting high and low theoretical prices."""
        theoretical_prices = [bs, binomial, heston]
        max_price = max(theoretical_prices)
        min_price = min(theoretical_prices)
        
        classes = {'market': '', 'bs': '', 'binomial': '', 'heston': ''}
        
        if bs == max_price:
            classes['bs'] = 'price-high'
        elif bs == min_price:
            classes['bs'] = 'price-low'
            
        if binomial == max_price:
            classes['binomial'] = 'price-high'
        elif binomial == min_price:
            classes['binomial'] = 'price-low'
            
        if heston == max_price:
            classes['heston'] = 'price-high'
        elif heston == min_price:
            classes['heston'] = 'price-low'
        
        return classes
    
    def calculate_scenario_spread_prices(self, long_option: Dict[str, Any], short_option: Dict[str, Any], 
                                       scenario_price: float, expiration_date: str) -> Dict[str, float]:
        """Calculate spread net prices for a given underlying price scenario."""
        time_to_exp = self.pricing_engine.get_time_to_expiration(expiration_date)
        
        # Get implied volatilities from the options
        long_iv = long_option.get('estimated_iv', 0.20)
        short_iv = short_option.get('estimated_iv', 0.20)
        
        # Calculate theoretical prices for the long option at scenario price
        if long_option['option_type'].lower() == 'call':
            long_bs = self.pricing_engine.black_scholes_call(scenario_price, long_option['strike'], time_to_exp, long_iv)
        else:
            long_bs = self.pricing_engine.black_scholes_put(scenario_price, long_option['strike'], time_to_exp, long_iv)
        
        long_binomial = self.pricing_engine.binomial_option_price(scenario_price, long_option['strike'], time_to_exp, long_iv, long_option['option_type'])
        long_heston = self.pricing_engine.heston_option_price(scenario_price, long_option['strike'], time_to_exp, long_iv, long_option['option_type'], n_paths=25000)
        
        # Calculate theoretical prices for the short option at scenario price
        if short_option['option_type'].lower() == 'call':
            short_bs = self.pricing_engine.black_scholes_call(scenario_price, short_option['strike'], time_to_exp, short_iv)
        else:
            short_bs = self.pricing_engine.black_scholes_put(scenario_price, short_option['strike'], time_to_exp, short_iv)
        
        short_binomial = self.pricing_engine.binomial_option_price(scenario_price, short_option['strike'], time_to_exp, short_iv, short_option['option_type'])
        short_heston = self.pricing_engine.heston_option_price(scenario_price, short_option['strike'], time_to_exp, short_iv, short_option['option_type'], n_paths=25000)
        
        # Calculate net spread prices (long - short)
        return {
            'bs': long_bs - short_bs,
            'binomial': long_binomial - short_binomial,
            'heston': long_heston - short_heston
        }

    def generate_consolidated_html_report(self, put_result: Dict[str, Any], call_result: Dict[str, Any], 
                                        ticker: str, current_price: float, 
                                        output_file: str = "spread_analysis.html") -> str:
        """Generate a consolidated HTML report for both put and call spread analyses."""
        
        # Extract data for both analyses
        put_long = put_result['long_option']
        put_short = put_result['short_option']
        put_spread = put_result['spread_analysis']
        
        call_long = call_result['long_option']
        call_short = call_result['short_option']
        call_spread = call_result['spread_analysis']
        
        # Extract VRP data (should be same for both put and call results)
        vrp_data = put_result.get('vrp_analysis', {})
        vrp_signal = vrp_data.get('vrp_signal')
        put_vrp_rec = put_result.get('vrp_recommendation', {})
        call_vrp_rec = call_result.get('vrp_recommendation', {})
        
        # Calculate display values for puts
        put_long_mid = (put_long['bid'] + put_long['ask']) / 2 if put_long['bid'] > 0 and put_long['ask'] > 0 else put_long['market_mid']
        put_short_mid = (put_short['bid'] + put_short['ask']) / 2 if put_short['bid'] > 0 and put_short['ask'] > 0 else put_short['market_mid']
        put_net_debit = put_long_mid - put_short_mid
        put_net_bs = put_long.get('black_scholes_price', 0) - put_short.get('black_scholes_price', 0)
        put_net_binomial = put_long.get('binomial_price', 0) - put_short.get('binomial_price', 0)
        put_net_heston = put_long.get('heston_price', 0) - put_short.get('heston_price', 0)
        
        # Calculate display values for calls
        call_long_mid = (call_long['bid'] + call_long['ask']) / 2 if call_long['bid'] > 0 and call_long['ask'] > 0 else call_long['market_mid']
        call_short_mid = (call_short['bid'] + call_short['ask']) / 2 if call_short['bid'] > 0 and call_short['ask'] > 0 else call_short['market_mid']
        call_net_debit = call_long_mid - call_short_mid
        call_net_bs = call_long.get('black_scholes_price', 0) - call_short.get('black_scholes_price', 0)
        call_net_binomial = call_long.get('binomial_price', 0) - call_short.get('binomial_price', 0)
        call_net_heston = call_long.get('heston_price', 0) - call_short.get('heston_price', 0)
        
        expiration_date = put_result['expiration_date']
        price_range = put_result['price_range']
        spread_width = put_result['spread_width']
        
        # Calculate scenario prices for proper bid reference
        put_high_prices = self.calculate_scenario_spread_prices(put_long, put_short, current_price + price_range, expiration_date)
        put_current_prices = {'bs': put_net_bs, 'binomial': put_net_binomial, 'heston': put_net_heston}
        put_low_prices = self.calculate_scenario_spread_prices(put_long, put_short, current_price - price_range, expiration_date)
        
        call_high_prices = self.calculate_scenario_spread_prices(call_long, call_short, current_price + price_range, expiration_date)
        call_current_prices = {'bs': call_net_bs, 'binomial': call_net_binomial, 'heston': call_net_heston}
        call_low_prices = self.calculate_scenario_spread_prices(call_long, call_short, current_price - price_range, expiration_date)
        
        # Generate VRP section HTML
        vrp_section_html = self._generate_vrp_section_html(vrp_signal, put_vrp_rec, call_vrp_rec) if vrp_signal else ""
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Consolidated Spread Analysis - {ticker}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            font-size: 12px;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 24px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
            font-size: 18px;
        }}
        h3 {{
            color: #2c3e50;
            font-size: 16px;
        }}
        .summary {{
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 13px;
        }}
        .vrp-section {{
            background-color: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }}
        .vrp-header {{
            color: #2c3e50;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }}
        .vrp-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }}
        .vrp-metrics {{
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }}
        .vrp-signal {{
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
        }}
        .vrp-recommendations {{
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }}
        .signal-strong-sell {{ color: #dc3545; font-weight: bold; }}
        .signal-sell {{ color: #fd7e14; font-weight: bold; }}
        .signal-neutral {{ color: #6c757d; font-weight: bold; }}
        .signal-buy {{ color: #28a745; font-weight: bold; }}
        .signal-strong-buy {{ color: #20c997; font-weight: bold; }}
        .alignment-favorable {{ color: #28a745; font-weight: bold; }}
        .alignment-unfavorable {{ color: #dc3545; font-weight: bold; }}
        .alignment-neutral {{ color: #6c757d; font-weight: bold; }}
        .analysis-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 11px;
        }}
        th, td {{
            padding: 8px;
            text-align: right;
            border: 1px solid #ddd;
        }}
        th {{
            background-color: #3498db;
            color: white;
            font-weight: bold;
            font-size: 10px;
        }}
        .position {{
            text-align: left !important;
            font-weight: bold;
        }}
        .long-row {{
            background-color: #d5f4e6;
        }}
        .short-row {{
            background-color: #ffeaa7;
        }}
        .net-row {{
            background-color: #74b9ff;
            color: white;
            font-weight: bold;
        }}
        .current-market-row {{
            background-color: #bdc3c7;
            color: #2c3e50;
            font-weight: bold;
        }}
        .scenario-row {{
            background-color: #f8f9fa;
        }}
        .metrics {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }}
        .metric-box {{
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }}
        .metric-label {{
            font-weight: bold;
            color: #2c3e50;
            font-size: 11px;
        }}
        .metric-value {{
            font-size: 14px;
            color: #27ae60;
            margin-top: 5px;
        }}
        .price-high {{
            color: #dc3545 !important;
            font-weight: bold;
        }}
        .price-low {{
            color: #28a745 !important;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>CONSOLIDATED SPREAD ANALYSIS</h1>
        
        <div class="summary">
            <strong>Underlying:</strong> {ticker} @ ${current_price:.2f} &nbsp;&nbsp;&nbsp;
            <strong>Expiration:</strong> {expiration_date} &nbsp;&nbsp;&nbsp;
            <strong>Spread Width:</strong> {spread_width:.0f} points &nbsp;&nbsp;&nbsp;
            <strong>Analysis:</strong> Bear Put Spread + Bull Call Spread
        </div>

        {vrp_section_html}

        <div class="analysis-grid">
            <div>
                <h3>🐻 BEAR PUT SPREAD</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Position</th>
                            <th>Strike</th>
                            <th>Bid</th>
                            <th>Ask</th>
                            <th>Market</th>
                            <th>B-S</th>
                            <th>Binomial</th>
                            <th>Heston</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="long-row">
                            <td class="position">LONG</td>
                            <td>${put_long['strike']:.0f}</td>
                            <td>${put_long['bid']:.2f}</td>
                            <td>${put_long['ask']:.2f}</td>
                            <td>${put_long_mid:.2f}</td>
                            <td>${put_long.get('black_scholes_price', 0):.2f}</td>
                            <td>${put_long.get('binomial_price', 0):.2f}</td>
                            <td>${put_long.get('heston_price', 0):.2f}</td>
                        </tr>
                        <tr class="short-row">
                            <td class="position">SHORT</td>
                            <td>${put_short['strike']:.0f}</td>
                            <td>${put_short['bid']:.2f}</td>
                            <td>${put_short['ask']:.2f}</td>
                            <td>${put_short_mid:.2f}</td>
                            <td>${put_short.get('black_scholes_price', 0):.2f}</td>
                            <td>${put_short.get('binomial_price', 0):.2f}</td>
                            <td>${put_short.get('heston_price', 0):.2f}</td>
                        </tr>
                        <tr class="net-row">
                            <td class="position">NET</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>${put_net_debit:.2f}</td>
                            <td>${put_net_bs:.2f}</td>
                            <td>${put_net_binomial:.2f}</td>
                            <td>${put_net_heston:.2f}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="metrics">
                    <div class="metric-box">
                        <div class="metric-label">Net Debit</div>
                        <div class="metric-value">${put_spread['net_debit']:.2f}</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Max Profit</div>
                        <div class="metric-value">${put_spread['max_profit']:.2f}</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Breakeven</div>
                        <div class="metric-value">${put_spread['breakeven']:.2f}</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Distance to Breakeven</div>
                        <div class="metric-value">${current_price - put_spread['breakeven']:.2f} pts ({((current_price - put_spread['breakeven']) / current_price * 100):.1f}%)</div>
                    </div>
                </div>
            </div>
            
            <div>
                <h3>🐂 BULL CALL SPREAD</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Position</th>
                            <th>Strike</th>
                            <th>Bid</th>
                            <th>Ask</th>
                            <th>Market</th>
                            <th>B-S</th>
                            <th>Binomial</th>
                            <th>Heston</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="long-row">
                            <td class="position">LONG</td>
                            <td>${call_long['strike']:.0f}</td>
                            <td>${call_long['bid']:.2f}</td>
                            <td>${call_long['ask']:.2f}</td>
                            <td>${call_long_mid:.2f}</td>
                            <td>${call_long.get('black_scholes_price', 0):.2f}</td>
                            <td>${call_long.get('binomial_price', 0):.2f}</td>
                            <td>${call_long.get('heston_price', 0):.2f}</td>
                        </tr>
                        <tr class="short-row">
                            <td class="position">SHORT</td>
                            <td>${call_short['strike']:.0f}</td>
                            <td>${call_short['bid']:.2f}</td>
                            <td>${call_short['ask']:.2f}</td>
                            <td>${call_short_mid:.2f}</td>
                            <td>${call_short.get('black_scholes_price', 0):.2f}</td>
                            <td>${call_short.get('binomial_price', 0):.2f}</td>
                            <td>${call_short.get('heston_price', 0):.2f}</td>
                        </tr>
                        <tr class="net-row">
                            <td class="position">NET</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>${call_net_debit:.2f}</td>
                            <td>${call_net_bs:.2f}</td>
                            <td>${call_net_binomial:.2f}</td>
                            <td>${call_net_heston:.2f}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="metrics">
                    <div class="metric-box">
                        <div class="metric-label">Net Debit</div>
                        <div class="metric-value">${call_spread['net_debit']:.2f}</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Max Profit</div>
                        <div class="metric-value">${call_spread['max_profit']:.2f}</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Breakeven</div>
                        <div class="metric-value">${call_spread['breakeven']:.2f}</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Distance to Breakeven</div>
                        <div class="metric-value">${call_spread['breakeven'] - current_price:.2f} pts ({((call_spread['breakeven'] - current_price) / current_price * 100):.1f}%)</div>
                    </div>
                </div>
            </div>
        </div>

        <h2>📊 Consolidated Bid Reference</h2>
        <div class="analysis-grid">
            <div>
                <h3>🐻 Bear Put Spread - Bid Reference</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Scenario</th>
                            <th>Price</th>
                            <th>Market</th>
                            <th>B-S</th>
                            <th>Binomial</th>
                            <th>Heston</th>
                            <th>Recommendation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="scenario-row">
                            <td class="position">High (+{price_range:.0f})</td>
                            <td>${current_price + price_range:.0f}</td>
                            <td>-</td>
                            <td class="{self.get_price_classes(put_net_debit, put_high_prices['bs'], put_high_prices['binomial'], put_high_prices['heston'])['bs']}">${put_high_prices['bs']:.2f}</td>
                            <td class="{self.get_price_classes(put_net_debit, put_high_prices['bs'], put_high_prices['binomial'], put_high_prices['heston'])['binomial']}">${put_high_prices['binomial']:.2f}</td>
                            <td class="{self.get_price_classes(put_net_debit, put_high_prices['bs'], put_high_prices['binomial'], put_high_prices['heston'])['heston']}">${put_high_prices['heston']:.2f}</td>
                            <td>Lower value when underlying rises</td>
                        </tr>
                        <tr class="current-market-row">
                            <td class="position">Current</td>
                            <td>${current_price:.0f}</td>
                            <td>${put_net_debit:.2f}</td>
                            <td class="{self.get_price_classes(put_net_debit, put_current_prices['bs'], put_current_prices['binomial'], put_current_prices['heston'])['bs']}">${put_current_prices['bs']:.2f}</td>
                            <td class="{self.get_price_classes(put_net_debit, put_current_prices['bs'], put_current_prices['binomial'], put_current_prices['heston'])['binomial']}">${put_current_prices['binomial']:.2f}</td>
                            <td class="{self.get_price_classes(put_net_debit, put_current_prices['bs'], put_current_prices['binomial'], put_current_prices['heston'])['heston']}">${put_current_prices['heston']:.2f}</td>
                            <td><strong>Current spread value</strong></td>
                        </tr>
                        <tr class="scenario-row">
                            <td class="position">Low (-{price_range:.0f})</td>
                            <td>${current_price - price_range:.0f}</td>
                            <td>-</td>
                            <td class="{self.get_price_classes(put_net_debit, put_low_prices['bs'], put_low_prices['binomial'], put_low_prices['heston'])['bs']}">${put_low_prices['bs']:.2f}</td>
                            <td class="{self.get_price_classes(put_net_debit, put_low_prices['bs'], put_low_prices['binomial'], put_low_prices['heston'])['binomial']}">${put_low_prices['binomial']:.2f}</td>
                            <td class="{self.get_price_classes(put_net_debit, put_low_prices['bs'], put_low_prices['binomial'], put_low_prices['heston'])['heston']}">${put_low_prices['heston']:.2f}</td>
                            <td>Higher value when underlying falls</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="background-color: #ffeaa7; padding: 12px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #e17055;">
                    <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">🎯 Put Spread Strategy</h4>
                    <ul style="margin-bottom: 0; font-size: 11px;">
                        <li><strong>Current Cost:</strong> ${put_net_debit:.2f}</li>
                        <li><strong>Breakeven:</strong> ${put_spread['breakeven']:.0f}</li>
                        <li><strong>Max Profit:</strong> ${put_spread['max_profit']:.2f}</li>
                        <li><strong>Best When:</strong> {ticker} falls below {put_spread['breakeven']:.0f}</li>
                    </ul>
                </div>
            </div>
            
            <div>
                <h3>🐂 Bull Call Spread - Bid Reference</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Scenario</th>
                            <th>Price</th>
                            <th>Market</th>
                            <th>B-S</th>
                            <th>Binomial</th>
                            <th>Heston</th>
                            <th>Recommendation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="scenario-row">
                            <td class="position">High (+{price_range:.0f})</td>
                            <td>${current_price + price_range:.0f}</td>
                            <td>-</td>
                            <td class="{self.get_price_classes(call_net_debit, call_high_prices['bs'], call_high_prices['binomial'], call_high_prices['heston'])['bs']}">${call_high_prices['bs']:.2f}</td>
                            <td class="{self.get_price_classes(call_net_debit, call_high_prices['bs'], call_high_prices['binomial'], call_high_prices['heston'])['binomial']}">${call_high_prices['binomial']:.2f}</td>
                            <td class="{self.get_price_classes(call_net_debit, call_high_prices['bs'], call_high_prices['binomial'], call_high_prices['heston'])['heston']}">${call_high_prices['heston']:.2f}</td>
                            <td>Higher value when underlying rises</td>
                        </tr>
                        <tr class="current-market-row">
                            <td class="position">Current</td>
                            <td>${current_price:.0f}</td>
                            <td>${call_net_debit:.2f}</td>
                            <td class="{self.get_price_classes(call_net_debit, call_current_prices['bs'], call_current_prices['binomial'], call_current_prices['heston'])['bs']}">${call_current_prices['bs']:.2f}</td>
                            <td class="{self.get_price_classes(call_net_debit, call_current_prices['bs'], call_current_prices['binomial'], call_current_prices['heston'])['binomial']}">${call_current_prices['binomial']:.2f}</td>
                            <td class="{self.get_price_classes(call_net_debit, call_current_prices['bs'], call_current_prices['binomial'], call_current_prices['heston'])['heston']}">${call_current_prices['heston']:.2f}</td>
                            <td><strong>Current spread value</strong></td>
                        </tr>
                        <tr class="scenario-row">
                            <td class="position">Low (-{price_range:.0f})</td>
                            <td>${current_price - price_range:.0f}</td>
                            <td>-</td>
                            <td class="{self.get_price_classes(call_net_debit, call_low_prices['bs'], call_low_prices['binomial'], call_low_prices['heston'])['bs']}">${call_low_prices['bs']:.2f}</td>
                            <td class="{self.get_price_classes(call_net_debit, call_low_prices['bs'], call_low_prices['binomial'], call_low_prices['heston'])['binomial']}">${call_low_prices['binomial']:.2f}</td>
                            <td class="{self.get_price_classes(call_net_debit, call_low_prices['bs'], call_low_prices['binomial'], call_low_prices['heston'])['heston']}">${call_low_prices['heston']:.2f}</td>
                            <td>Lower value when underlying falls</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="background-color: #d5f4e6; padding: 12px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #00b894;">
                    <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">🎯 Call Spread Strategy</h4>
                    <ul style="margin-bottom: 0; font-size: 11px;">
                        <li><strong>Current Cost:</strong> ${call_net_debit:.2f}</li>
                        <li><strong>Breakeven:</strong> ${call_spread['breakeven']:.0f}</li>
                        <li><strong>Max Profit:</strong> ${call_spread['max_profit']:.2f}</li>
                        <li><strong>Best When:</strong> {ticker} rises above {call_spread['breakeven']:.0f}</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #3498db;">
            <h3 style="margin-top: 0; color: #2c3e50;">💡 Overall Bidding Strategy</h3>
            <ul style="margin-bottom: 0; font-size: 12px;">
                <li><strong>Bear Put Spread:</strong> ${put_net_debit:.2f} - Profits when {ticker} falls below ${put_spread['breakeven']:.0f}</li>
                <li><strong>Bull Call Spread:</strong> ${call_net_debit:.2f} - Profits when {ticker} rises above ${call_spread['breakeven']:.0f}</li>
                <li><strong>Market Outlook:</strong> Choose put spread for bearish view, call spread for bullish view</li>
                <li><strong>Risk Management:</strong> Both spreads have limited risk (max loss = net debit paid)</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 10px;">
            Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </div>
    </div>
</body>
</html>
        """
        
        # Write HTML to file
        with open(output_file, 'w') as f:
            f.write(html_content)
        
        print(f"📊 Consolidated HTML report generated: {output_file}")
        print(f"🐻 Bear Put Spread: ${put_net_debit:.2f} (Breakeven: ${put_spread['breakeven']:.0f})")
        print(f"🐂 Bull Call Spread: ${call_net_debit:.2f} (Breakeven: ${call_spread['breakeven']:.0f})")
        print(f"🔗 Opening in browser...")
        
        return output_file 
    
    def _generate_vrp_section_html(self, vrp_signal, put_vrp_rec: Dict[str, Any], call_vrp_rec: Dict[str, Any]) -> str:
        """Generate HTML for VRP analysis section."""
        if not vrp_signal:
            return ""
        
        # Get signal emoji and CSS class
        signal_emojis = {
            "Strong Sell": "🔴",
            "Sell": "🟠", 
            "Neutral": "🟡",
            "Buy": "🟢",
            "Strong Buy": "🟢"
        }
        
        signal_classes = {
            "Strong Sell": "signal-strong-sell",
            "Sell": "signal-sell",
            "Neutral": "signal-neutral",
            "Buy": "signal-buy",
            "Strong Buy": "signal-strong-buy"
        }
        
        alignment_classes = {
            "favorable": "alignment-favorable",
            "unfavorable": "alignment-unfavorable",
            "neutral": "alignment-neutral"
        }
        
        emoji = signal_emojis.get(vrp_signal.signal_strength, "⚪")
        signal_class = signal_classes.get(vrp_signal.signal_strength, "signal-neutral")
        
        put_alignment_class = alignment_classes.get(put_vrp_rec.get('vrp_alignment', 'neutral'), 'alignment-neutral')
        call_alignment_class = alignment_classes.get(call_vrp_rec.get('vrp_alignment', 'neutral'), 'alignment-neutral')
        
        # Generate interpretation text
        avg_vrp = (vrp_signal.vrp_30d + vrp_signal.vrp_60d) / 2
        if avg_vrp > 0.05:
            interpretation = "Options appear OVERPRICED - consider selling premium"
            interpretation_color = "#dc3545"
        elif avg_vrp < -0.05:
            interpretation = "Options appear UNDERPRICED - consider buying options"
            interpretation_color = "#28a745"
        else:
            interpretation = "Options appear FAIRLY VALUED - neutral positioning"
            interpretation_color = "#6c757d"
        
        return f"""
        <div class="vrp-section">
            <div class="vrp-header">🎯 VRP (Volatility Risk Premium) Analysis</div>
            
            <div class="vrp-grid">
                <div class="vrp-metrics">
                    <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">📊 Volatility Metrics</h4>
                    <div style="font-size: 12px; line-height: 1.6;">
                        <div><strong>Current IV:</strong> {vrp_signal.current_iv:.1%}</div>
                        <div><strong>30-Day Realized:</strong> {vrp_signal.realized_vol_30d:.1%}</div>
                        <div><strong>60-Day Realized:</strong> {vrp_signal.realized_vol_60d:.1%}</div>
                        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #dee2e6;">
                            <div><strong>30-Day VRP:</strong> <span style="color: {'#dc3545' if vrp_signal.vrp_30d > 0 else '#28a745'};">{vrp_signal.vrp_30d:+.1%}</span></div>
                            <div><strong>60-Day VRP:</strong> <span style="color: {'#dc3545' if vrp_signal.vrp_60d > 0 else '#28a745'};">{vrp_signal.vrp_60d:+.1%}</span></div>
                            <div><strong>VRP Percentile:</strong> {vrp_signal.vrp_percentile:.0f}%</div>
                        </div>
                        {self._generate_historical_vrp_html(vrp_signal.historical_vrp_data) if vrp_signal.historical_vrp_data and vrp_signal.historical_vrp_data.get('days_analyzed', 0) > 0 else ''}
                    </div>
                </div>
                
                <div class="vrp-signal">
                    <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">{emoji} Trading Signal</h4>
                    <div style="font-size: 12px; line-height: 1.6;">
                        <div><strong>Signal:</strong> <span class="{signal_class}">{vrp_signal.signal_strength}</span></div>
                        <div><strong>Confidence:</strong> {vrp_signal.confidence:.0%}</div>
                        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #dee2e6;">
                            <div><strong>Strategy:</strong></div>
                            <div style="font-size: 11px; margin-top: 5px; font-style: italic;">{vrp_signal.recommended_strategy}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="vrp-recommendations">
                <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">💡 Strategy Alignment</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 12px;">
                    <div>
                        <div><strong>🐻 Bear Put Spread:</strong> <span class="{put_alignment_class}">{put_vrp_rec.get('vrp_alignment', 'neutral').title()}</span></div>
                        <div style="font-size: 11px; margin-top: 5px; color: #6c757d;">{put_vrp_rec.get('reasoning', 'No VRP analysis available')}</div>
                        {f'<div style="font-size: 11px; margin-top: 5px; color: #e74c3c;"><strong>Alternative:</strong> {put_vrp_rec["alternative_strategy"]}</div>' if put_vrp_rec.get('alternative_strategy') else ''}
                    </div>
                    <div>
                        <div><strong>🐂 Bull Call Spread:</strong> <span class="{call_alignment_class}">{call_vrp_rec.get('vrp_alignment', 'neutral').title()}</span></div>
                        <div style="font-size: 11px; margin-top: 5px; color: #6c757d;">{call_vrp_rec.get('reasoning', 'No VRP analysis available')}</div>
                        {f'<div style="font-size: 11px; margin-top: 5px; color: #e74c3c;"><strong>Alternative:</strong> {call_vrp_rec["alternative_strategy"]}</div>' if call_vrp_rec.get('alternative_strategy') else ''}
                    </div>
                </div>
                
                <div style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid {interpretation_color};">
                    <div style="font-weight: bold; color: {interpretation_color}; font-size: 12px;">📈 Market Interpretation:</div>
                    <div style="font-size: 11px; margin-top: 5px; color: #2c3e50;">{interpretation}</div>
                </div>
            </div>
        </div>
        """
    
    def _generate_historical_vrp_html(self, hist_data: Dict[str, Any]) -> str:
        """Generate HTML for historical VRP comparison section."""
        if not hist_data or hist_data.get('days_analyzed', 0) == 0:
            return ""
        
        # Determine trend color and icon
        trend_info = {
            'increasing': {'color': '#dc3545', 'icon': '📈', 'text': 'Increasing'},
            'decreasing': {'color': '#28a745', 'icon': '📉', 'text': 'Decreasing'},
            'stable': {'color': '#6c757d', 'icon': '➡️', 'text': 'Stable'},
            'insufficient_data': {'color': '#6c757d', 'icon': '❓', 'text': 'Insufficient Data'},
            'error': {'color': '#dc3545', 'icon': '❌', 'text': 'Error'}
        }
        
        trend = hist_data.get('vrp_trend', 'insufficient_data')
        trend_style = trend_info.get(trend, trend_info['insufficient_data'])
        
        # Determine current vs average interpretation
        current_vs_avg = hist_data.get('current_vs_avg', 0.0)
        if current_vs_avg > 0.02:
            vs_avg_color = '#dc3545'
            vs_avg_text = 'Significantly above recent average'
        elif current_vs_avg < -0.02:
            vs_avg_color = '#28a745'
            vs_avg_text = 'Significantly below recent average'
        else:
            vs_avg_color = '#6c757d'
            vs_avg_text = 'Near recent average'
        
        return f"""
                        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #dee2e6;">
                            <div style="font-weight: bold; color: #2c3e50; margin-bottom: 5px;">📈 Historical Comparison ({hist_data['days_analyzed']} days)</div>
                            <div><strong>20-Day Avg VRP:</strong> {hist_data['avg_vrp_30d']:+.1%}</div>
                            <div><strong>Current vs Avg:</strong> <span style="color: {vs_avg_color};">{current_vs_avg:+.1%}</span></div>
                            <div><strong>VRP Range:</strong> {hist_data['min_vrp']:+.1%} to {hist_data['max_vrp']:+.1%}</div>
                            <div><strong>Trend:</strong> <span style="color: {trend_style['color']};">{trend_style['icon']} {trend_style['text']}</span></div>
                            <div style="font-size: 10px; margin-top: 5px; color: {vs_avg_color}; font-style: italic;">{vs_avg_text}</div>
                        </div>
        """ 