# VRP (Volatility Risk Premium) Analysis Guide

This guide explains the VRP (Volatility Risk Premium) functionality in the Options Spread Analyzer and how to use it for better options trading decisions.

## 🎯 What is VRP?

**Volatility Risk Premium (VRP)** is the difference between implied volatility and realized volatility:

```
VRP = Implied Volatility - Realized Volatility
```

### Key Concepts:

- **Positive VRP**: Options are overpriced relative to historical volatility → **Sell premium**
- **Negative VRP**: Options are underpriced relative to historical volatility → **Buy options**
- **Neutral VRP**: Options are fairly valued → **Neutral strategies**

## 🚀 Quick Start

### Standalone VRP Analysis
```bash
# Basic VRP analysis for SPX
python vrp_demo.py

# Custom ticker and parameters
python vrp_demo.py --ticker AAPL --price 175 --month 12 --year 2024

# Stock analysis (vs index)
python vrp_demo.py --ticker AAPL --stock
```

### Integrated with Spread Analysis
```bash
# VRP is automatically included in spread analysis
python main.py --ticker SPX --month 7 --year 2025
```

## 📊 VRP Components

### 1. Implied Volatility (IV)
- Extracted from at-the-money option prices
- Represents market's expectation of future volatility
- Forward-looking measure

### 2. Realized Volatility (RV)
- Calculated from historical price movements
- Uses 30-day and 60-day lookback periods
- Backward-looking measure

### 3. VRP Calculation
```python
# 30-day VRP
vrp_30d = current_iv - realized_vol_30d

# 60-day VRP  
vrp_60d = current_iv - realized_vol_60d
```

## 🎯 VRP Signals

### Signal Strength Levels:

| VRP Range | Signal | Confidence | Strategy |
|-----------|--------|------------|----------|
| > +8% | Strong Sell | 90% | Sell straddles, iron condors |
| +4% to +8% | Sell | 70% | Credit spreads, covered calls |
| -4% to +4% | Neutral | 50% | Delta-neutral, wait |
| -8% to -4% | Buy | 70% | Debit spreads, protective puts |
| < -8% | Strong Buy | 90% | Buy straddles, long volatility |

### VRP Percentile Ranking:
- **80-100%**: Very high VRP (top 20% historically)
- **60-80%**: Above average VRP
- **40-60%**: Average VRP
- **20-40%**: Below average VRP
- **0-20%**: Very low VRP (bottom 20% historically)

## 🔧 API Reference

### VRPAnalyzer Class

#### `analyze_vrp(symbol, current_price=None, exp_month=None, exp_year=None, is_index=True)`
Performs comprehensive VRP analysis.

**Parameters:**
- `symbol` (str): Ticker symbol
- `current_price` (float): Current price (fetched if None)
- `exp_month` (int): Expiration month for IV calculation
- `exp_year` (int): Expiration year for IV calculation
- `is_index` (bool): Whether symbol is an index

**Returns:**
- `VRPSignal`: Complete VRP analysis results

#### `display_vrp_analysis(vrp_signal)`
Displays formatted VRP analysis results.

#### `get_vrp_for_spread_analysis(symbol, current_price, exp_month, exp_year)`
Returns VRP data for integration with spread analysis.

### VRPSignal Data Structure

```python
@dataclass
class VRPSignal:
    symbol: str
    current_iv: float
    realized_vol_30d: float
    realized_vol_60d: float
    vrp_30d: float
    vrp_60d: float
    vrp_percentile: float
    signal_strength: str  # 'Strong Sell', 'Sell', 'Neutral', 'Buy', 'Strong Buy'
    confidence: float
    recommended_strategy: str
    timestamp: datetime
```

## 📈 Trading Applications

### 1. Strategy Selection
```python
# High VRP → Sell premium strategies
if vrp_signal.vrp_30d > 0.05:
    strategies = ["Bear put spreads", "Iron condors", "Covered calls"]

# Low VRP → Buy options strategies  
elif vrp_signal.vrp_30d < -0.05:
    strategies = ["Bull call spreads", "Long straddles", "Protective puts"]
```

### 2. Position Sizing
```python
# Higher confidence → Larger position size
position_size = base_size * vrp_signal.confidence
```

### 3. Entry Timing
```python
# Wait for extreme VRP readings
if vrp_signal.vrp_percentile > 80 or vrp_signal.vrp_percentile < 20:
    # High conviction trade
    execute_strategy()
```

## 🔍 VRP Integration with Spread Analysis

When running spread analysis, VRP is automatically calculated and provides:

### 1. Strategy Alignment Assessment
- **Favorable**: Strategy aligns with VRP signal
- **Unfavorable**: Strategy conflicts with VRP signal  
- **Neutral**: Strategy is reasonably aligned

### 2. Risk Assessment
- **Low Risk**: VRP supports the strategy
- **Moderate Risk**: Neutral VRP environment
- **High Risk**: VRP suggests alternative strategies

### 3. Alternative Strategy Suggestions
When VRP conflicts with chosen strategy, alternatives are suggested:
- High VRP + Bull call spread → Suggests bear put spread
- Low VRP + Bear put spread → Suggests bull call spread

## 📊 Example Output

```
🎯 VRP ANALYSIS: SPX
============================================================
📅 Analysis Time: 2024-01-15 14:30:25

📊 VOLATILITY METRICS:
   Current IV:        18.5%
   30-Day Realized:   12.3%
   60-Day Realized:   14.1%

🎯 VRP CALCULATIONS:
   30-Day VRP:        +6.2%
   60-Day VRP:        +4.4%
   VRP Percentile:    75%

🔴 TRADING SIGNAL:
   Signal:            Sell
   Confidence:        70%
   Strategy:          Sell premium strategies, credit spreads

💡 INTERPRETATION:
   Options appear OVERPRICED - consider selling premium
============================================================
```

## ⚙️ Configuration

### Historical Data Settings
- **Default lookback**: 252 days (1 year) for percentile calculation
- **Volatility periods**: 30-day and 60-day realized volatility
- **Price frequency**: Daily closing prices

### VRP Thresholds
You can customize VRP thresholds in `vrp_analyzer.py`:

```python
# Modify these values to adjust signal sensitivity
STRONG_SELL_THRESHOLD = 0.08  # 8%
SELL_THRESHOLD = 0.04         # 4%
BUY_THRESHOLD = -0.04         # -4%
STRONG_BUY_THRESHOLD = -0.08  # -8%
```

## 🚨 Important Notes

### 1. Market Hours
- VRP analysis works best during market hours
- Historical data may be limited for some symbols
- Synthetic data is generated when API fails

### 2. Data Quality
- Implied volatility requires liquid options markets
- ATM options are used for IV calculation
- Falls back to 20% default volatility if data unavailable

### 3. Limitations
- VRP is a statistical measure, not a guarantee
- Market conditions can change rapidly
- Should be combined with other analysis methods

## 🔄 Integration Examples

### 1. Programmatic Usage
```python
from market_data import MarketData
from pricing_engine import PricingEngine
from vrp_analyzer import VRPAnalyzer

# Initialize
market_data = MarketData()
pricing_engine = PricingEngine()
vrp_analyzer = VRPAnalyzer(market_data, pricing_engine)

# Analyze VRP
vrp_signal = vrp_analyzer.analyze_vrp('SPX', 5800.0, 7, 2025)

# Check signal
if vrp_signal.signal_strength == "Strong Sell":
    print("Execute premium selling strategy")
```

### 2. Batch Analysis
```python
symbols = ['SPX', 'NDX', 'RUT']
for symbol in symbols:
    vrp_signal = vrp_analyzer.analyze_vrp(symbol)
    print(f"{symbol}: {vrp_signal.signal_strength} ({vrp_signal.vrp_30d:+.1%})")
```

## 📚 Further Reading

- **VIX vs VRP**: VIX measures implied volatility, VRP compares it to realized volatility
- **Term Structure**: VRP can vary across different expiration dates
- **Market Regimes**: VRP patterns differ in bull vs bear markets
- **Volatility Clustering**: High volatility periods tend to cluster together

## 🎯 Best Practices

1. **Combine with Technical Analysis**: Use VRP alongside price action and support/resistance
2. **Monitor Multiple Timeframes**: Check both 30-day and 60-day VRP
3. **Consider Market Environment**: VRP interpretation may vary in different market conditions
4. **Risk Management**: Always use proper position sizing and stop losses
5. **Backtesting**: Test VRP strategies on historical data before live trading 