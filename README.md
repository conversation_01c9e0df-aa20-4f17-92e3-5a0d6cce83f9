# Options Spread Analyzer with Modular Architecture

A comprehensive Python tool for analyzing options spreads with real market data from Polygon API compared against Black-Scholes, Binomial, and Heston theoretical pricing models. Features modular class architecture for better maintainability and price highlighting for visual analysis.

## Key Features

- **🏗️ Modular Architecture**: Organized into separate classes for market data, pricing engines, spread analysis, and report generation
- **📊 Real Market Data**: Retrieves live options quotes from Polygon API
- **🔴 Real-Time Streaming**: Live index price updates via WebSocket connections
- **🎯 VRP Analysis**: Volatility Risk Premium analysis for better strategy selection
- **🧮 Multi-Model Pricing**: Three theoretical pricing models:
  - **Black-Scholes**: Classic analytical model with Greeks
  - **Binomial Trees**: Discrete-time model with 100-step trees
  - **Heston Stochastic Volatility**: Advanced Monte Carlo model with time-varying volatility
- **🎯 Price Highlighting**: Visual highlighting in bid reference tables - light green for highest theoretical prices, light red for lowest
- **📈 Model Comparison**: Side-by-side comparison of market vs. all theoretical models
- **📋 Scenario Analysis**: Shows theoretical prices at ±configurable points from current price
- **📄 HTML Reports**: Professional consolidated reports with responsive design
- **🚀 Flexible Interface**: Command-line arguments or programmatic usage

## Architecture

### Modular Class Structure

The codebase is organized into five main classes for separation of concerns:

#### 1. **MarketData** (`market_data.py`)
- Handles all API calls and market data retrieval from Polygon.io
- **Key Methods**:
  - `get_current_price()` - handles index (I: prefix) vs stock formatting with real-time support
  - `enable_realtime_streaming()` - start live WebSocket price feeds
  - `get_realtime_price()` - get latest cached real-time price
  - `get_third_friday()` - calculates options expiration dates
  - `retrieve_options()` / `retrieve_options_by_date()` - fetch options contracts
- **Features**: API key management, WebSocket streaming, real-time price caching, error handling

#### 2. **PricingEngine** (`pricing_engine.py`)
- Handles all option pricing models and calculations
- **Models**: Black-Scholes, Binomial (100-step trees), Heston (Monte Carlo)
- **Key Methods**:
  - Pricing: `black_scholes_call/put()`, `binomial_option_price()`, `heston_option_price()`
  - Greeks: Complete Greeks calculation for all three models
  - Utilities: `estimate_implied_volatility()`, `get_time_to_expiration()`
- **Features**: Newton-Raphson IV estimation, finite differences for Greeks

#### 3. **SpreadAnalyzer** (`spread_analyzer.py`)
- Handles options spread calculations, pricing, and business logic
- **Dependencies**: Uses MarketData and PricingEngine instances
- **Key Methods**:
  - `price_options()` - comprehensive pricing with all three models
  - `price_spread()` - calculates net debit, max profit/loss, breakeven
  - `analyze_spread()` - full spread analysis for bear put and bull call strategies
- **Features**: Strategy-specific calculations, fallback pricing, comprehensive Greeks, VRP integration

#### 4. **VRPAnalyzer** (`vrp_analyzer.py`)
- Handles Volatility Risk Premium analysis for strategy selection
- **Key Methods**:
  - `analyze_vrp()` - comprehensive VRP analysis with signal generation
  - `get_historical_prices()` - fetches historical data for realized volatility
  - `calculate_realized_volatility()` - computes 30-day and 60-day realized volatility
  - `get_atm_implied_volatility()` - extracts implied volatility from ATM options
- **Features**: VRP signal generation, strategy recommendations, percentile rankings

#### 5. **ReportGenerator** (`report_generator.py`)
- Handles HTML report generation and output formatting
- **Key Methods**:
  - `generate_consolidated_html_report()` - creates comprehensive HTML with both spread types
  - `get_price_classes()` - price highlighting logic for visual assessment
- **Features**: Responsive design, color coding, consolidated bid reference tables with highlighting

## VRP (Volatility Risk Premium) Analysis

The tool now includes comprehensive VRP analysis to help with strategy selection:

### What is VRP?
**VRP = Implied Volatility - Realized Volatility**

- **Positive VRP**: Options are overpriced → Sell premium strategies
- **Negative VRP**: Options are underpriced → Buy options strategies
- **Neutral VRP**: Options fairly valued → Neutral strategies

### VRP Features
- **📊 Real-Time Analysis**: Calculates current implied vs realized volatility
- **📈 Historical Context**: VRP percentile rankings over 1-year lookback
- **🎯 Signal Generation**: Strong Buy/Sell signals with confidence levels
- **💡 Strategy Recommendations**: Suggests optimal strategies based on VRP
- **🔄 Integration**: Automatically included in spread analysis

### VRP Usage

#### Standalone VRP Analysis
```bash
# Basic VRP analysis for SPX
python vrp_demo.py

# Custom parameters
python vrp_demo.py --ticker AAPL --price 175 --month 12 --year 2024 --stock
```

#### Integrated with Spread Analysis
VRP analysis is automatically included when running the main analysis:
```bash
python main.py  # VRP analysis included automatically
```

### VRP Signal Interpretation

| VRP Range | Signal | Strategy Recommendation |
|-----------|--------|------------------------|
| > +8% | Strong Sell | Sell straddles, iron condors |
| +4% to +8% | Sell | Credit spreads, covered calls |
| -4% to +4% | Neutral | Delta-neutral, wait for opportunities |
| -8% to -4% | Buy | Debit spreads, protective puts |
| < -8% | Strong Buy | Buy straddles, long volatility |

For detailed VRP documentation, see [VRP_GUIDE.md](VRP_GUIDE.md).

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Set up your Polygon API key
cp .env.example .env
# Edit .env and add your POLYGON_API_KEY
```

## Real-Time Streaming

The tool now supports real-time index price streaming via WebSocket connections to Polygon.io:

### Features
- **🔴 Live Price Updates**: Real-time SPX index price updates
- **⚡ Automatic Fallback**: Falls back to API calls if streaming unavailable
- **🔄 Smart Caching**: Caches latest prices for immediate access
- **🧵 Background Processing**: Non-blocking WebSocket connections

### Quick Real-Time Demo
```bash
# Basic real-time price streaming
python realtime_demo.py

# Real-time analysis with automatic re-calculation on price changes
python realtime_demo.py --analysis
```

## Usage

### Command Line Interface

#### Default SPX Analysis (Both Put and Call Spreads)
```bash
python main.py
```

#### Custom Parameters
```bash
# Bull call spread on AAPL
python main.py --ticker AAPL --stock --price 175 --month 12 --year 2024

# Bear put spread with custom parameters
python main.py -t SPX -p 5800 -m 7 -y 2025 --rate 0.045 --spread-width 50

# Enable real-time streaming for SPX
python main.py --realtime

# Real-time analysis with custom parameters
python main.py --realtime -t SPX -m 7 -y 2025 --spread-width 50
```

#### Help
```bash
python main.py --help
```

### Programmatic Usage

```python
from main import quick_analysis

# Quick analysis with defaults (analyzes single spread type)
result = quick_analysis("SPX", price=5803, month=7, year=2025, option_type="put")

# Quick analysis with custom parameters
result = quick_analysis("AAPL", price=175, month=12, year=2024, option_type="call", 
                       spread_width=25, price_range=5, is_index=False)

# Using individual components with real-time streaming
from market_data import MarketData
from pricing_engine import PricingEngine
from spread_analyzer import SpreadAnalyzer
from report_generator import ReportGenerator

# Initialize components
market_data = MarketData()
pricing_engine = PricingEngine(risk_free_rate=0.05)
spread_analyzer = SpreadAnalyzer(market_data, pricing_engine)
report_generator = ReportGenerator()

# Enable real-time streaming
market_data.enable_realtime_streaming(['SPX'])

# Get current price (will use real-time if available)
current_price = market_data.get_current_price("SPX", is_index=True, use_realtime=True)
result = spread_analyzer.analyze_spread("SPX", current_price, 7, 2025, "put")

# Set up real-time callbacks
def on_price_update(symbol, price, data):
    print(f"New {symbol} price: ${price:.2f}")
    
market_data.add_price_update_callback(on_price_update)
```

## Output Format

The tool generates professional consolidated HTML reports featuring both bear put and bull call spreads:

### 🎨 **Price Highlighting Feature**
Visual highlighting in bid reference tables helps assess market price fairness:
- **Light Green**: Highest theoretical price among the three models
- **Light Red**: Lowest theoretical price among the three models
- **No Highlighting**: Middle theoretical price

This helps quickly identify which theoretical model suggests the option is most/least expensive relative to market pricing.

### Key Sections in Consolidated HTML Report:

1. **📊 Side-by-Side Spread Comparison**
   - Bear Put Spread (left) and Bull Call Spread (right)
   - Market prices, bid/ask, and all three theoretical models
   - Net spread calculations for both strategies

2. **🎯 Consolidated Bid Reference Tables**
   - **Price highlighting** showing highest/lowest theoretical values
   - Scenario analysis for different underlying price levels
   - Strategy-specific recommendations for each spread

3. **📈 Strategy Summaries**
   - Bear Put: Profits when underlying falls below breakeven
   - Bull Call: Profits when underlying rises above breakeven
   - Current costs, max profits, and breakeven levels

4. **💡 Overall Bidding Strategy**
   - Market outlook guidance (bearish vs bullish)
   - Risk management notes
   - Comparative analysis between both strategies

The HTML file is saved as `spread_analysis.html` and the analysis results are printed to console.

## How It Works

### Spread Strategies

The tool automatically analyzes **both** spread strategies:

1. **🐻 Bear Put Spread**:
   - Long put at current price (rounded to nearest spread width)
   - Short put [spread_width] points below
   - Profits when underlying falls below breakeven

2. **🐂 Bull Call Spread**:
   - Long call at current price (rounded to nearest spread width)  
   - Short call [spread_width] points above
   - Profits when underlying rises above breakeven

### Pricing Models Comparison

- **Black-Scholes Model**: 
  - Analytical solution using log-normal distribution
  - Constant volatility assumption
  - Fast, precise calculations

- **Binomial Tree Model**:
  - Discrete-time model with 100 time steps
  - Risk-neutral valuation working backwards from expiration
  - Handles path-dependent features better

- **Heston Stochastic Volatility Model**:
  - Advanced model with time-varying volatility (CIR process)
  - Monte Carlo simulation with 50,000 paths
  - Captures volatility smile and market dynamics
  - Most realistic but computationally intensive

- **Market Comparison**: Shows differences between market prices and all three models

### Price Highlighting Logic

The `get_price_classes()` method compares theoretical prices and applies CSS classes:
```python
def get_price_classes(self, market, bs, binomial, heston):
    theoretical_prices = [bs, binomial, heston]
    max_price = max(theoretical_prices)
    min_price = min(theoretical_prices)
    
    # Assign 'price-high' to highest, 'price-low' to lowest
    # Helps assess if market price is fair relative to models
```

## Command Line Arguments

| Argument | Short | Type | Default | Description |
|----------|--------|------|---------|-------------|
| `--ticker` | `-t` | str | SPX | Stock/index ticker symbol |
| `--price` | `-p` | float | API | Current price (fetched from API if not provided) |
| `--month` | `-m` | int | 7 | Expiration month (1-12) |
| `--year` | `-y` | int | 2025 | Expiration year |
| `--rate` | `-r` | float | 0.05 | Risk-free rate |
| `--spread-width` | `-w` | float | 100 | Strike price spread width in points |
| `--price-range` | `-g` | float | 10 | Price range for scenario analysis in points |
| `--index` | `-i` | flag | True | Treat ticker as index (adds i: prefix for API) |
| `--stock` | `-s` | flag | False | Treat ticker as stock (no i: prefix for API) |

## Architecture Benefits

The modular class architecture provides:

- ✅ **Separation of Concerns**: Each class has single responsibility
- ✅ **Maintainability**: Easier to modify individual components
- ✅ **Testability**: Classes can be unit tested independently
- ✅ **Reusability**: Components can be used in different contexts
- ✅ **Dependency Injection**: SpreadAnalyzer accepts MarketData and PricingEngine instances
- ✅ **Extensibility**: Easy to add new pricing models or data sources

## Examples

### SPX Index Analysis (Default - Both Spreads)
```bash
python main.py
# Analyzes both bear put and bull call spreads for SPX index
```

### Stock Analysis with Manual Price
```bash
python main.py --ticker AAPL --stock --price 175 --month 3 --year 2025
# Analyzes AAPL stock with manual price override for both spread types
```

### Custom Spread Width and Price Range
```bash
python main.py --ticker SPX --spread-width 50 --price-range 20
# Uses 50-point spreads with ±20 point scenario analysis
```