#!/usr/bin/env python3
"""
VRP (Volatility Risk Premium) Demo
Demonstrates the VRP analysis functionality for options trading.
"""

import argparse
import sys
from market_data import MarketData
from pricing_engine import PricingEngine
from vrp_analyzer import VRPAnalyzer


def main():
    """Main VRP demo function."""
    parser = argparse.ArgumentParser(description='VRP Analysis Demo')
    parser.add_argument('--ticker', '-t', type=str, default='SPX',
                       help='Ticker symbol to analyze (default: SPX)')
    parser.add_argument('--price', '-p', type=float, default=None,
                       help='Current price (will fetch from API if not provided)')
    parser.add_argument('--month', '-m', type=int, default=7,
                       help='Expiration month for IV calculation (default: 7)')
    parser.add_argument('--year', '-y', type=int, default=2025,
                       help='Expiration year for IV calculation (default: 2025)')
    parser.add_argument('--index', action='store_true', default=True,
                       help='Treat ticker as index (default: True)')
    parser.add_argument('--stock', dest='index', action='store_false',
                       help='Treat ticker as stock')
    
    args = parser.parse_args()
    
    try:
        print("🎯 VRP (Volatility Risk Premium) Analysis Demo")
        print("=" * 60)
        
        # Initialize components
        market_data = MarketData()
        pricing_engine = PricingEngine()
        vrp_analyzer = VRPAnalyzer(market_data, pricing_engine)
        
        # Get current price if not provided
        if args.price is None:
            try:
                current_price = market_data.get_current_price(args.ticker, args.index)
                print(f"📊 Retrieved current price: ${current_price:.2f}")
            except Exception as e:
                print(f"❌ Failed to retrieve price: {e}")
                print("💡 Please provide price using --price parameter")
                return 1
        else:
            current_price = args.price
            print(f"📊 Using provided price: ${current_price:.2f}")
        
        # Perform VRP analysis
        vrp_signal = vrp_analyzer.analyze_vrp(
            symbol=args.ticker,
            current_price=current_price,
            exp_month=args.month,
            exp_year=args.year,
            is_index=args.index
        )
        
        # Display results
        vrp_analyzer.display_vrp_analysis(vrp_signal)
        
        # Show trading implications
        print(f"\n💡 TRADING IMPLICATIONS:")
        print(f"{'='*60}")
        
        if vrp_signal.vrp_30d > 0.05:
            print("🔴 HIGH VRP - Options are expensive!")
            print("   Recommended strategies:")
            print("   • Sell straddles/strangles")
            print("   • Iron condors")
            print("   • Covered calls")
            print("   • Credit spreads")
        elif vrp_signal.vrp_30d < -0.05:
            print("🟢 LOW VRP - Options are cheap!")
            print("   Recommended strategies:")
            print("   • Buy straddles/strangles")
            print("   • Long volatility plays")
            print("   • Debit spreads")
            print("   • Protective puts")
        else:
            print("🟡 NEUTRAL VRP - Options fairly valued")
            print("   Recommended strategies:")
            print("   • Delta-neutral strategies")
            print("   • Wait for better opportunities")
            print("   • Small position sizes")
        
        # Show historical context
        print(f"\n📈 HISTORICAL CONTEXT:")
        print(f"{'='*60}")
        print(f"VRP Percentile: {vrp_signal.vrp_percentile:.0f}%")
        
        if vrp_signal.vrp_percentile >= 80:
            print("📊 VRP is in the top 20% historically - very high")
        elif vrp_signal.vrp_percentile >= 60:
            print("📊 VRP is above average - moderately high")
        elif vrp_signal.vrp_percentile <= 20:
            print("📊 VRP is in the bottom 20% historically - very low")
        elif vrp_signal.vrp_percentile <= 40:
            print("📊 VRP is below average - moderately low")
        else:
            print("📊 VRP is around historical average")
        
        print(f"\n✅ VRP analysis completed successfully!")
        return 0
        
    except Exception as e:
        print(f"❌ Error in VRP analysis: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 