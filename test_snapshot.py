#!/usr/bin/env python3
"""
Test script to check Polygon API bulk snapshot response for SPX options
"""

import requests
import json
from dotenv import load_dotenv
import os

load_dotenv()

def test_bulk_snapshot():
    api_key = os.getenv('POLYGON_API_KEY')
    
    # Try bulk snapshot for SPX options
    url = "https://api.polygon.io/v3/snapshot/options/SPX"
    params = {
        'apikey': api_key,
        'limit': 250  # Get many results to understand coverage
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()
        
        print(f"Status: {data.get('status')}")
        print(f"Results count: {len(data.get('results', []))}")
        
        # Analyze what's available
        strikes = []
        expirations = []
        contracts_with_greeks = 0
        contracts_with_iv = 0
        
        if data.get('results'):
            for result in data['results']:
                details = result.get('details', {})
                strike = details.get('strike_price', 0)
                exp_date = details.get('expiration_date', '')
                greeks = result.get('greeks', {})
                
                strikes.append(strike)
                expirations.append(exp_date)
                
                if greeks and any(greeks.values()):
                    contracts_with_greeks += 1
                
                if 'implied_volatility' in result and result['implied_volatility']:
                    contracts_with_iv += 1
        
        print(f"\nStrike range: {min(strikes)} to {max(strikes)}")
        print(f"Unique expirations: {sorted(set(expirations))}")
        print(f"Contracts with Greeks: {contracts_with_greeks}")
        print(f"Contracts with IV: {contracts_with_iv}")
        
        # Show a few sample contracts to understand structure
        print(f"\nFirst 3 contracts:")
        for i, result in enumerate(data['results'][:3]):
            details = result.get('details', {})
            greeks = result.get('greeks', {})
            print(f"\n--- Contract {i+1} ---")
            print(f"Ticker: {details.get('ticker', 'N/A')}")
            print(f"Strike: {details.get('strike_price', 'N/A')}")
            print(f"Type: {details.get('contract_type', 'N/A')}")
            print(f"Expiration: {details.get('expiration_date', 'N/A')}")
            print(f"Greeks: {greeks}")
            print(f"All result keys: {list(result.keys())}")
            if 'implied_volatility' in result:
                print(f"IV: {result['implied_volatility']}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_bulk_snapshot() 