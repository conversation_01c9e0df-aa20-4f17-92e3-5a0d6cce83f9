#!/usr/bin/env python3
"""
Test script for VRP functionality
Verifies that VRP analysis works correctly and integrates with the system.
"""

import sys
from constants import (
    TEST_SYMBOL_SPX, TEST_PRICE_SPX, TEST_MONTH, TEST_YEAR,
    <PERSON>IG<PERSON>L_STRONG_SELL, S<PERSON>NAL_SELL, SIGNAL_NEUTRAL, SIGNAL_BUY, SIGNAL_STRONG_BUY
)
from market_data import MarketData
from pricing_engine import PricingEngine
from vrp_analyzer import VRPAnalyzer, VRPSignal
from spread_analyzer import SpreadAnalyzer


def test_vrp_analyzer():
    """Test VRP analyzer functionality."""
    print("🧪 Testing VRP Analyzer...")
    
    try:
        # Initialize components
        market_data = MarketData()
        pricing_engine = PricingEngine()
        vrp_analyzer = VRPAnalyzer(market_data, pricing_engine)
        
        # Test VRP analysis with provided price
        vrp_signal = vrp_analyzer.analyze_vrp(TEST_SYMBOL_SPX, current_price=TEST_PRICE_SPX, exp_month=TEST_MONTH, exp_year=TEST_YEAR)

        # Verify VRP signal structure
        assert isinstance(vrp_signal, VRPSignal), "VRP signal should be VRPSignal instance"
        assert vrp_signal.symbol == TEST_SYMBOL_SPX, f"Symbol should be {TEST_SYMBOL_SPX}"
        assert isinstance(vrp_signal.current_iv, float), "Current IV should be float"
        assert isinstance(vrp_signal.vrp_30d, float), "VRP 30d should be float"
        assert vrp_signal.signal_strength in [SIGNAL_STRONG_SELL, SIGNAL_SELL, SIGNAL_NEUTRAL, SIGNAL_BUY, SIGNAL_STRONG_BUY], "Invalid signal strength"
        
        print(f"✅ VRP Analysis: {vrp_signal.signal_strength} ({vrp_signal.vrp_30d:+.1%})")
        return True
        
    except Exception as e:
        print(f"❌ VRP Analyzer test failed: {e}")
        return False


def test_vrp_integration():
    """Test VRP integration with spread analyzer."""
    print("🧪 Testing VRP Integration...")
    
    try:
        # Initialize components
        market_data = MarketData()
        pricing_engine = PricingEngine()
        spread_analyzer = SpreadAnalyzer(market_data, pricing_engine)
        
        # Test spread analysis with VRP integration
        result = spread_analyzer.analyze_spread(
            ticker='SPX',
            current_price=5800.0,
            exp_month=7,
            exp_year=2025,
            option_type='put'
        )
        
        # Verify VRP data is included
        assert 'vrp_analysis' in result, "VRP analysis should be included in results"
        assert 'vrp_recommendation' in result, "VRP recommendation should be included"
        
        vrp_data = result['vrp_analysis']
        vrp_rec = result['vrp_recommendation']
        
        assert 'vrp_signal' in vrp_data, "VRP signal should be in VRP data"
        assert 'vrp_alignment' in vrp_rec, "VRP alignment should be in recommendation"
        
        print(f"✅ VRP Integration: {vrp_rec['vrp_alignment']} alignment")
        return True
        
    except Exception as e:
        print(f"❌ VRP Integration test failed: {e}")
        return False


def test_vrp_signals():
    """Test VRP signal generation logic."""
    print("🧪 Testing VRP Signal Generation...")
    
    try:
        market_data = MarketData()
        pricing_engine = PricingEngine()
        vrp_analyzer = VRPAnalyzer(market_data, pricing_engine)
        
        # Test different VRP scenarios
        test_cases = [
            (0.10, 0.09, SIGNAL_STRONG_SELL),  # High VRP
            (0.05, 0.04, SIGNAL_SELL),         # Moderate high VRP
            (0.01, 0.00, SIGNAL_NEUTRAL),      # Low VRP
            (-0.05, -0.04, SIGNAL_BUY),        # Moderate low VRP
            (-0.10, -0.09, SIGNAL_STRONG_BUY)  # Very low VRP
        ]
        
        for vrp_30d, vrp_60d, expected_signal in test_cases:
            signal, confidence, strategy = vrp_analyzer.generate_vrp_signal(vrp_30d, vrp_60d, 50.0)
            
            if signal != expected_signal:
                print(f"❌ Signal mismatch: VRP {vrp_30d:+.1%} expected {expected_signal}, got {signal}")
                return False
        
        print("✅ VRP Signal Generation: All test cases passed")
        return True
        
    except Exception as e:
        print(f"❌ VRP Signal Generation test failed: {e}")
        return False


def test_vrp_data_structure():
    """Test VRP data structure and methods."""
    print("🧪 Testing VRP Data Structure...")
    
    try:
        market_data = MarketData()
        pricing_engine = PricingEngine()
        vrp_analyzer = VRPAnalyzer(market_data, pricing_engine)
        
        # Test get_vrp_for_spread_analysis method
        vrp_data = vrp_analyzer.get_vrp_for_spread_analysis(TEST_SYMBOL_SPX, TEST_PRICE_SPX, TEST_MONTH, TEST_YEAR)
        
        required_keys = [
            'vrp_signal', 'current_iv', 'realized_vol_30d', 'vrp_30d',
            'vrp_percentile', 'signal_strength', 'confidence',
            'recommended_strategy', 'is_options_expensive', 'is_options_cheap'
        ]
        
        for key in required_keys:
            assert key in vrp_data, f"Missing key: {key}"
        
        # Test boolean flags
        assert isinstance(vrp_data['is_options_expensive'], bool), "is_options_expensive should be boolean"
        assert isinstance(vrp_data['is_options_cheap'], bool), "is_options_cheap should be boolean"
        
        print("✅ VRP Data Structure: All required fields present")
        return True
        
    except Exception as e:
        print(f"❌ VRP Data Structure test failed: {e}")
        return False


def main():
    """Run all VRP tests."""
    print("🚀 Starting VRP Functionality Tests")
    print("=" * 50)
    
    tests = [
        ("VRP Analyzer", test_vrp_analyzer),
        ("VRP Integration", test_vrp_integration),
        ("VRP Signal Generation", test_vrp_signals),
        ("VRP Data Structure", test_vrp_data_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All VRP tests passed!")
        return 0
    else:
        print("💡 Some VRP tests failed - check implementation")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 