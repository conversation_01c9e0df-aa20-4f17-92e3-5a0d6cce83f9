
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Consolidated Spread Analysis - SPX</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            font-size: 12px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 24px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
            font-size: 18px;
        }
        h3 {
            color: #2c3e50;
            font-size: 16px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 13px;
        }
        .vrp-section {
            background-color: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .vrp-header {
            color: #2c3e50;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        .vrp-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        .vrp-metrics {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .vrp-signal {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
        }
        .vrp-recommendations {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .signal-strong-sell { color: #dc3545; font-weight: bold; }
        .signal-sell { color: #fd7e14; font-weight: bold; }
        .signal-neutral { color: #6c757d; font-weight: bold; }
        .signal-buy { color: #28a745; font-weight: bold; }
        .signal-strong-buy { color: #20c997; font-weight: bold; }
        .alignment-favorable { color: #28a745; font-weight: bold; }
        .alignment-unfavorable { color: #dc3545; font-weight: bold; }
        .alignment-neutral { color: #6c757d; font-weight: bold; }
        .analysis-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 11px;
        }
        th, td {
            padding: 8px;
            text-align: right;
            border: 1px solid #ddd;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
            font-size: 10px;
        }
        .position {
            text-align: left !important;
            font-weight: bold;
        }
        .long-row {
            background-color: #d5f4e6;
        }
        .short-row {
            background-color: #ffeaa7;
        }
        .net-row {
            background-color: #74b9ff;
            color: white;
            font-weight: bold;
        }
        .current-market-row {
            background-color: #bdc3c7;
            color: #2c3e50;
            font-weight: bold;
        }
        .scenario-row {
            background-color: #f8f9fa;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .metric-box {
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .metric-label {
            font-weight: bold;
            color: #2c3e50;
            font-size: 11px;
        }
        .metric-value {
            font-size: 14px;
            color: #27ae60;
            margin-top: 5px;
        }
        .price-high {
            color: #dc3545 !important;
            font-weight: bold;
        }
        .price-low {
            color: #28a745 !important;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CONSOLIDATED SPREAD ANALYSIS</h1>
        
        <div class="summary">
            <strong>Underlying:</strong> SPX @ $6259.75 &nbsp;&nbsp;&nbsp;
            <strong>Expiration:</strong> 2025-07-18 &nbsp;&nbsp;&nbsp;
            <strong>Spread Width:</strong> 100 points &nbsp;&nbsp;&nbsp;
            <strong>Analysis:</strong> Bear Put Spread + Bull Call Spread
        </div>

        
        <div class="vrp-section">
            <div class="vrp-header">🎯 VRP (Volatility Risk Premium) Analysis</div>
            
            <div class="vrp-grid">
                <div class="vrp-metrics">
                    <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">📊 Volatility Metrics</h4>
                    <div style="font-size: 12px; line-height: 1.6;">
                        <div><strong>Current IV:</strong> 13.3%</div>
                        <div><strong>30-Day Realized:</strong> 9.0%</div>
                        <div><strong>60-Day Realized:</strong> 15.2%</div>
                        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #dee2e6;">
                            <div><strong>30-Day VRP:</strong> <span style="color: #dc3545;">+4.3%</span></div>
                            <div><strong>60-Day VRP:</strong> <span style="color: #28a745;">-1.9%</span></div>
                            <div><strong>VRP Percentile:</strong> 60%</div>
                        </div>
                        
                        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #dee2e6;">
                            <div style="font-weight: bold; color: #2c3e50; margin-bottom: 5px;">📈 Historical Comparison (20 days)</div>
                            <div><strong>20-Day Avg VRP:</strong> +1.0%</div>
                            <div><strong>Current vs Avg:</strong> <span style="color: #dc3545;">+3.3%</span></div>
                            <div><strong>VRP Range:</strong> -1.3% to +4.3%</div>
                            <div><strong>Trend:</strong> <span style="color: #dc3545;">📈 Increasing</span></div>
                            <div style="font-size: 10px; margin-top: 5px; color: #dc3545; font-style: italic;">Significantly above recent average</div>
                        </div>
        
                    </div>
                </div>
                
                <div class="vrp-signal">
                    <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">🟡 Trading Signal</h4>
                    <div style="font-size: 12px; line-height: 1.6;">
                        <div><strong>Signal:</strong> <span class="signal-neutral">Neutral</span></div>
                        <div><strong>Confidence:</strong> 50%</div>
                        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #dee2e6;">
                            <div><strong>Strategy:</strong></div>
                            <div style="font-size: 11px; margin-top: 5px; font-style: italic;">Delta-neutral strategies, wait for better opportunities</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="vrp-recommendations">
                <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">💡 Strategy Alignment</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 12px;">
                    <div>
                        <div><strong>🐻 Bear Put Spread:</strong> <span class="alignment-favorable">Favorable</span></div>
                        <div style="font-size: 11px; margin-top: 5px; color: #6c757d;">VRP of +4.3% suggests options are overpriced. Bear put spread benefits from high volatility premium.</div>
                        
                    </div>
                    <div>
                        <div><strong>🐂 Bull Call Spread:</strong> <span class="alignment-unfavorable">Unfavorable</span></div>
                        <div style="font-size: 11px; margin-top: 5px; color: #6c757d;">VRP of +4.3% suggests options are overpriced. Consider selling strategies instead.</div>
                        <div style="font-size: 11px; margin-top: 5px; color: #e74c3c;"><strong>Alternative:</strong> Bear put spread or iron condor</div>
                    </div>
                </div>
                
                <div style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #6c757d;">
                    <div style="font-weight: bold; color: #6c757d; font-size: 12px;">📈 Market Interpretation:</div>
                    <div style="font-size: 11px; margin-top: 5px; color: #2c3e50;">Options appear FAIRLY VALUED - neutral positioning</div>
                </div>
            </div>
        </div>
        

        <div class="analysis-grid">
            <div>
                <h3>🐻 BEAR PUT SPREAD</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Position</th>
                            <th>Strike</th>
                            <th>Bid</th>
                            <th>Ask</th>
                            <th>Market</th>
                            <th>B-S</th>
                            <th>Binomial</th>
                            <th>Heston</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="long-row">
                            <td class="position">LONG</td>
                            <td>$6300</td>
                            <td>$60.70</td>
                            <td>$61.70</td>
                            <td>$61.20</td>
                            <td>$61.21</td>
                            <td>$61.13</td>
                            <td>$60.74</td>
                        </tr>
                        <tr class="short-row">
                            <td class="position">SHORT</td>
                            <td>$6200</td>
                            <td>$18.60</td>
                            <td>$19.30</td>
                            <td>$18.95</td>
                            <td>$18.96</td>
                            <td>$19.01</td>
                            <td>$19.69</td>
                        </tr>
                        <tr class="net-row">
                            <td class="position">NET</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>$42.25</td>
                            <td>$42.25</td>
                            <td>$42.12</td>
                            <td>$41.05</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="metrics">
                    <div class="metric-box">
                        <div class="metric-label">Net Debit</div>
                        <div class="metric-value">$42.25</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Max Profit</div>
                        <div class="metric-value">$57.75</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Breakeven</div>
                        <div class="metric-value">$6257.75</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Distance to Breakeven</div>
                        <div class="metric-value">$2.00 pts (0.0%)</div>
                    </div>
                </div>
            </div>
            
            <div>
                <h3>🐂 BULL CALL SPREAD</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Position</th>
                            <th>Strike</th>
                            <th>Bid</th>
                            <th>Ask</th>
                            <th>Market</th>
                            <th>B-S</th>
                            <th>Binomial</th>
                            <th>Heston</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="long-row">
                            <td class="position">LONG</td>
                            <td>$6300</td>
                            <td>$17.10</td>
                            <td>$17.90</td>
                            <td>$17.50</td>
                            <td>$17.49</td>
                            <td>$17.56</td>
                            <td>$16.55</td>
                        </tr>
                        <tr class="short-row">
                            <td class="position">SHORT</td>
                            <td>$6400</td>
                            <td>$0.90</td>
                            <td>$1.20</td>
                            <td>$1.05</td>
                            <td>$1.05</td>
                            <td>$1.04</td>
                            <td>$0.50</td>
                        </tr>
                        <tr class="net-row">
                            <td class="position">NET</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>$16.45</td>
                            <td>$16.44</td>
                            <td>$16.52</td>
                            <td>$16.05</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="metrics">
                    <div class="metric-box">
                        <div class="metric-label">Net Debit</div>
                        <div class="metric-value">$16.45</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Max Profit</div>
                        <div class="metric-value">$83.55</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Breakeven</div>
                        <div class="metric-value">$6316.45</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-label">Distance to Breakeven</div>
                        <div class="metric-value">$56.70 pts (0.9%)</div>
                    </div>
                </div>
            </div>
        </div>

        <h2>📊 Consolidated Bid Reference</h2>
        <div class="analysis-grid">
            <div>
                <h3>🐻 Bear Put Spread - Bid Reference</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Scenario</th>
                            <th>Price</th>
                            <th>Market</th>
                            <th>B-S</th>
                            <th>Binomial</th>
                            <th>Heston</th>
                            <th>Recommendation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="scenario-row">
                            <td class="position">High (+20)</td>
                            <td>$6280</td>
                            <td>-</td>
                            <td class="price-high">$35.26</td>
                            <td class="">$35.11</td>
                            <td class="price-low">$34.37</td>
                            <td>Lower value when underlying rises</td>
                        </tr>
                        <tr class="current-market-row">
                            <td class="position">Current</td>
                            <td>$6260</td>
                            <td>$42.25</td>
                            <td class="price-high">$42.25</td>
                            <td class="">$42.12</td>
                            <td class="price-low">$41.05</td>
                            <td><strong>Current spread value</strong></td>
                        </tr>
                        <tr class="scenario-row">
                            <td class="position">Low (-20)</td>
                            <td>$6240</td>
                            <td>-</td>
                            <td class="price-high">$49.50</td>
                            <td class="">$49.41</td>
                            <td class="price-low">$48.37</td>
                            <td>Higher value when underlying falls</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="background-color: #ffeaa7; padding: 12px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #e17055;">
                    <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">🎯 Put Spread Strategy</h4>
                    <ul style="margin-bottom: 0; font-size: 11px;">
                        <li><strong>Current Cost:</strong> $42.25</li>
                        <li><strong>Breakeven:</strong> $6258</li>
                        <li><strong>Max Profit:</strong> $57.75</li>
                        <li><strong>Best When:</strong> SPX falls below 6258</li>
                    </ul>
                </div>
            </div>
            
            <div>
                <h3>🐂 Bull Call Spread - Bid Reference</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Scenario</th>
                            <th>Price</th>
                            <th>Market</th>
                            <th>B-S</th>
                            <th>Binomial</th>
                            <th>Heston</th>
                            <th>Recommendation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="scenario-row">
                            <td class="position">High (+20)</td>
                            <td>$6280</td>
                            <td>-</td>
                            <td class="price-low">$23.02</td>
                            <td class="price-high">$23.10</td>
                            <td class="">$23.02</td>
                            <td>Higher value when underlying rises</td>
                        </tr>
                        <tr class="current-market-row">
                            <td class="position">Current</td>
                            <td>$6260</td>
                            <td>$16.45</td>
                            <td class="">$16.44</td>
                            <td class="price-high">$16.52</td>
                            <td class="price-low">$16.05</td>
                            <td><strong>Current spread value</strong></td>
                        </tr>
                        <tr class="scenario-row">
                            <td class="position">Low (-20)</td>
                            <td>$6240</td>
                            <td>-</td>
                            <td class="">$11.21</td>
                            <td class="price-high">$11.25</td>
                            <td class="price-low">$10.32</td>
                            <td>Lower value when underlying falls</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="background-color: #d5f4e6; padding: 12px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #00b894;">
                    <h4 style="margin-top: 0; color: #2c3e50; font-size: 14px;">🎯 Call Spread Strategy</h4>
                    <ul style="margin-bottom: 0; font-size: 11px;">
                        <li><strong>Current Cost:</strong> $16.45</li>
                        <li><strong>Breakeven:</strong> $6316</li>
                        <li><strong>Max Profit:</strong> $83.55</li>
                        <li><strong>Best When:</strong> SPX rises above 6316</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #3498db;">
            <h3 style="margin-top: 0; color: #2c3e50;">💡 Overall Bidding Strategy</h3>
            <ul style="margin-bottom: 0; font-size: 12px;">
                <li><strong>Bear Put Spread:</strong> $42.25 - Profits when SPX falls below $6258</li>
                <li><strong>Bull Call Spread:</strong> $16.45 - Profits when SPX rises above $6316</li>
                <li><strong>Market Outlook:</strong> Choose put spread for bearish view, call spread for bullish view</li>
                <li><strong>Risk Management:</strong> Both spreads have limited risk (max loss = net debit paid)</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 10px;">
            Generated on 2025-07-13 15:19:01
        </div>
    </div>
</body>
</html>
        