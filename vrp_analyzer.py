#!/usr/bin/env python3
"""
VRP (Volatility Risk Premium) Analyzer Module
Calculates and analyzes the volatility risk premium for options trading strategies.

VRP = Implied Volatility - Realized Volatility

Positive VRP suggests options are overpriced (good for selling strategies)
Negative VRP suggests options are underpriced (good for buying strategies)
"""

import math
import numpy as np
import requests
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date, timedelta
import logging
from dataclasses import dataclass

from constants import (
    POLYGON_BASE_URL, POLYGON_SNAPSHOT_STOCKS, POLYGON_AGGREGATES,
    INDEX_PREFIX, DATE_FORMAT_API, API_PARAM_ADJUSTED, API_PARAM_SORT_ASC,
    API_PARAM_LIMIT_MAX, HISTORICAL_BUFFER_DAYS, VRP_STRONG_SELL_THRESHOLD,
    VRP_SELL_THRESHOLD, VRP_BUY_THRESHOLD, VRP_STRONG_BUY_THRESHOLD,
    VRP_PERCENTILE_VERY_LOW, VRP_PERCENTILE_LOW, VRP_PERCENTILE_NEUTRAL_LOW,
    VRP_PERCENTILE_NEUTRAL_HIGH, VRP_PERCENTILE_HIGH, VRP_PERCENTILE_VERY_HIGH,
    VRP_HIGH_CONFIDENCE, VRP_MEDIUM_CONFIDENCE, VRP_LOW_CONFIDENCE,
    TRADING_DAYS_IN_YEAR, VRP_SHORT_PERIOD, VRP_LONG_PERIOD,
    SIGNAL_STRONG_SELL, SIGNAL_SELL, SIGNAL_NEUTRAL, SIGNAL_BUY,
    SIGNAL_STRONG_BUY, SIGNAL_ERROR, STRATEGY_SELL_PREMIUM,
    STRATEGY_CREDIT_SPREADS, STRATEGY_DELTA_NEUTRAL, STRATEGY_DEBIT_SPREADS,
    STRATEGY_BUY_VOLATILITY, STRATEGY_ERROR, DEFAULT_VOLATILITY
)
from config import config
from market_data import MarketData
from pricing_engine import PricingEngine


@dataclass
class VRPSignal:
    """VRP trading signal data structure."""
    symbol: str
    current_iv: float
    realized_vol_30d: float
    realized_vol_60d: float
    vrp_30d: float
    vrp_60d: float
    vrp_percentile: float
    signal_strength: str  # 'Strong Sell', 'Sell', 'Neutral', 'Buy', 'Strong Buy'
    confidence: float
    recommended_strategy: str
    timestamp: datetime
    # Historical VRP data
    historical_vrp_data: Dict[str, Any] = None


class VRPAnalyzer:
    """Analyze Volatility Risk Premium for options trading."""
    
    def __init__(self, market_data: MarketData, pricing_engine: PricingEngine):
        """
        Initialize VRP Analyzer.
        
        Args:
            market_data (MarketData): Market data instance
            pricing_engine (PricingEngine): Pricing engine instance
        """
        self.market_data = market_data
        self.pricing_engine = pricing_engine
        self.vrp_history = {}  # Cache for VRP historical data
        self.silent_mode = False  # Controls console output
        
    def get_current_price_polygon(self, symbol: str, is_index: bool = True) -> float:
        """
        Get current price directly from Polygon API.
        
        Args:
            symbol (str): Symbol to get price for
            is_index (bool): Whether symbol is an index
            
        Returns:
            float: Current price
        """
        try:
            if is_index:
                # For indices, use snapshot endpoint with I: prefix
                api_symbol = f"{INDEX_PREFIX}{symbol.upper()}"
                url = f"{POLYGON_BASE_URL}{POLYGON_SNAPSHOT_STOCKS}/{api_symbol}"
            else:
                # For stocks
                api_symbol = symbol.upper()
                url = f"{POLYGON_BASE_URL}{POLYGON_SNAPSHOT_STOCKS}/{api_symbol}"
            
            params = {
                'apikey': self.market_data.api_key
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get('status') in ['OK', 'DELAYED'] and data.get('results'):
                result = data['results'][0]
                # Try different price fields in order of preference
                price = (result.get('value') or 
                        result.get('last_trade', {}).get('price') or
                        result.get('last_quote', {}).get('price') or
                        result.get('prevDay', {}).get('c'))
                
                if price:
                    if not self.silent_mode:
                        print(f"   📊 Current {symbol} price: ${float(price):.2f}")
                    return float(price)
            
            # Fallback to market_data method
            if not self.silent_mode:
                print(f"   ⚠️  Using fallback price method for {symbol}")
            return self.market_data.get_current_price(symbol, is_index)
            
        except Exception as e:
            logging.error(f"Error fetching current price for {symbol}: {e}")
            # Fallback to market_data method
            return self.market_data.get_current_price(symbol, is_index)

    def get_historical_prices(self, symbol: str, days: int = 252, is_index: bool = True) -> List[float]:
        """
        Get historical prices for volatility calculation via separate Polygon API call.
        
        Args:
            symbol (str): Symbol to get prices for
            days (int): Number of days of history (default: 252 for 1 year)
            is_index (bool): Whether symbol is an index
            
        Returns:
            List[float]: List of historical closing prices
        """
        try:
            # Calculate start and end dates
            end_date = date.today()
            start_date = end_date - timedelta(days=days + HISTORICAL_BUFFER_DAYS)  # Extra buffer for weekends/holidays

            # Format dates for API
            start_str = start_date.strftime(DATE_FORMAT_API)
            end_str = end_date.strftime(DATE_FORMAT_API)

            if is_index:
                # For indices, use aggregates endpoint with I: prefix
                api_symbol = f"{INDEX_PREFIX}{symbol.upper()}"
                url = f"{POLYGON_BASE_URL}{POLYGON_AGGREGATES}/{api_symbol}/range/1/day/{start_str}/{end_str}"
            else:
                # For stocks
                api_symbol = symbol.upper()
                url = f"{POLYGON_BASE_URL}{POLYGON_AGGREGATES}/{api_symbol}/range/1/day/{start_str}/{end_str}"
            
            params = {
                'adjusted': API_PARAM_ADJUSTED,
                'sort': API_PARAM_SORT_ASC,
                'limit': API_PARAM_LIMIT_MAX,
                'apikey': self.market_data.api_key
            }
            
            if not self.silent_mode:
                print(f"   📈 Fetching {days} days of historical data for {symbol}...")
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get('status') in ['OK', 'DELAYED'] and data.get('results'):
                prices = [float(result['c']) for result in data['results']]
                # Return the most recent 'days' prices
                final_prices = prices[-days:] if len(prices) >= days else prices
                
                if not self.silent_mode:
                    print(f"   ✅ Retrieved {len(final_prices)} historical prices")
                    print(f"      Price range: ${min(final_prices):.2f} - ${max(final_prices):.2f}")
                
                return final_prices
            else:
                if not self.silent_mode:
                    print(f"   ❌ No historical data found for {symbol}")
                return []
                
        except Exception as e:
            logging.error(f"Error fetching historical prices for {symbol}: {e}")
            if not self.silent_mode:
                print(f"   ❌ API error fetching historical data")
            return []
    

    
    def calculate_realized_volatility(self, prices: List[float], period_days: int = 30) -> float:
        """
        Calculate realized volatility from price series.
        
        Args:
            prices (List[float]): Historical prices
            period_days (int): Period for volatility calculation
            
        Returns:
            float: Annualized realized volatility, or None if insufficient data
        """
        if len(prices) < period_days + 1:
            return None  # Return None if insufficient data
        
        # Use the most recent period_days prices
        recent_prices = prices[-period_days-1:]
        
        # Calculate daily returns
        returns = []
        for i in range(1, len(recent_prices)):
            daily_return = math.log(recent_prices[i] / recent_prices[i-1])
            returns.append(daily_return)
        
        if not returns:
            return None
        
        # Calculate standard deviation of returns
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        daily_vol = math.sqrt(variance)
        
        # Annualize (252 trading days per year)
        annualized_vol = daily_vol * math.sqrt(252)
        
        return round(annualized_vol, 4)
    
    def get_comprehensive_implied_volatility(self, symbol: str, current_price: float, 
                                            exp_month: int, exp_year: int, 
                                            price_range: float = 100.0) -> float:
        """
        Get comprehensive implied volatility using all options within price range.
        
        Args:
            symbol (str): Underlying symbol
            current_price (float): Current price of underlying
            exp_month (int): Expiration month
            exp_year (int): Expiration year
            price_range (float): Price range around current price to include options (default: 100)
            
        Returns:
            float: Volume-weighted average implied volatility
        """
        try:
            # Calculate strike range
            min_strike = current_price - price_range
            max_strike = current_price + price_range
            
            # Round to nearest 50 for SPX strikes
            min_strike = max(50, round(min_strike / 50) * 50)
            max_strike = round(max_strike / 50) * 50
            
            if not self.silent_mode:
                print(f"   📊 Analyzing options from ${min_strike:.0f} to ${max_strike:.0f}")
            
            all_iv_data = []
            
            # Get options data for all strikes in range
            strike = min_strike
            while strike <= max_strike:
                try:
                    options_data = self.market_data.retrieve_options(symbol, strike, exp_month, exp_year)
                    
                    if options_data:
                        for option in options_data:
                            iv_data = self._calculate_option_iv(option, current_price, exp_month, exp_year)
                            if iv_data:
                                all_iv_data.append(iv_data)
                    
                except Exception as e:
                    if not self.silent_mode:
                        print(f"   ⚠️  Error getting options for strike ${strike}: {e}")
                
                strike += 50  # SPX strikes are typically in $50 increments
            
            if not all_iv_data:
                if not self.silent_mode:
                    print("   ⚠️  No valid options found, using default IV")
                return 0.20
            
            # Calculate volume-weighted average IV
            total_volume = sum(data['volume'] for data in all_iv_data)
            
            if total_volume > 0:
                weighted_iv = sum(data['iv'] * data['volume'] for data in all_iv_data) / total_volume
            else:
                # If no volume data, use simple average
                weighted_iv = sum(data['iv'] for data in all_iv_data) / len(all_iv_data)
            
            if not self.silent_mode:
                print(f"   ✅ Calculated IV from {len(all_iv_data)} options: {weighted_iv:.1%}")
                
                # Show breakdown by option type
                calls = [d for d in all_iv_data if d['type'] == 'call']
                puts = [d for d in all_iv_data if d['type'] == 'put']
                
                if calls:
                    call_avg = sum(d['iv'] for d in calls) / len(calls)
                    print(f"      Calls ({len(calls)}): {call_avg:.1%}")
                
                if puts:
                    put_avg = sum(d['iv'] for d in puts) / len(puts)
                    print(f"      Puts ({len(puts)}): {put_avg:.1%}")
            
            return round(weighted_iv, 4)
            
        except Exception as e:
            logging.error(f"Error calculating comprehensive IV for {symbol}: {e}")
            return 0.20
    
    def _calculate_option_iv(self, option: Dict[str, Any], current_price: float, 
                           exp_month: int, exp_year: int) -> Dict[str, Any]:
        """
        Calculate implied volatility for a single option.
        
        Args:
            option (Dict[str, Any]): Option data
            current_price (float): Current underlying price
            exp_month (int): Expiration month
            exp_year (int): Expiration year
            
        Returns:
            Dict[str, Any]: IV data with volume weighting info, or None if invalid
        """
        try:
            # Get market price (mid of bid/ask)
            bid = option.get('bid', 0)
            ask = option.get('ask', 0)
            last = option.get('last', 0)
            
            if bid > 0 and ask > 0:
                market_price = (bid + ask) / 2
                volume = option.get('volume', 1)  # Use 1 as default if no volume
            elif last > 0:
                market_price = last
                volume = option.get('volume', 1)
            else:
                return None
            
            # Skip options with very low prices (likely far OTM)
            if market_price < 0.50:
                return None
            
            # Calculate time to expiration
            expiration_date = self.market_data.get_third_friday(exp_year, exp_month)
            time_to_exp = self.pricing_engine.get_time_to_expiration(expiration_date)
            
            if time_to_exp <= 0:
                return None
            
            # Calculate implied volatility
            implied_vol = self.pricing_engine.estimate_implied_volatility(
                market_price, current_price, option['strike'], time_to_exp, option['option_type']
            )
            
            # Filter out unrealistic IV values
            if implied_vol < 0.05 or implied_vol > 2.0:  # 5% to 200%
                return None
            
            return {
                'iv': implied_vol,
                'volume': volume,
                'type': option['option_type'],
                'strike': option['strike'],
                'market_price': market_price,
                'moneyness': option['strike'] / current_price
            }
            
        except Exception as e:
            return None
    
    def calculate_vrp_percentile(self, current_vrp: float, symbol: str, 
                               lookback_days: int = 252) -> float:
        """
        Calculate VRP percentile ranking over historical period.
        
        Args:
            current_vrp (float): Current VRP value
            symbol (str): Symbol for historical context
            lookback_days (int): Days to look back for percentile calculation
            
        Returns:
            float: Percentile ranking (0-100)
        """
        # For now, use a simplified percentile calculation
        # In a full implementation, you'd store historical VRP values
        
        # Typical VRP ranges for SPX:
        # Very negative VRP: -0.10 to -0.05 (options cheap, percentile 0-20)
        # Negative VRP: -0.05 to 0.00 (options fairly priced, percentile 20-40)
        # Neutral VRP: 0.00 to 0.05 (slight premium, percentile 40-60)
        # Positive VRP: 0.05 to 0.10 (options expensive, percentile 60-80)
        # Very positive VRP: 0.10+ (options very expensive, percentile 80-100)
        
        if current_vrp <= -0.10:
            return 5.0
        elif current_vrp <= -0.05:
            return 20.0
        elif current_vrp <= 0.00:
            return 40.0
        elif current_vrp <= 0.05:
            return 60.0
        elif current_vrp <= 0.10:
            return 80.0
        else:
            return 95.0
    
    def calculate_historical_vrp(self, symbol: str, current_price: float, 
                               exp_month: int, exp_year: int, days: int = 20,
                               is_index: bool = True) -> Dict[str, Any]:
        """
        Calculate VRP for the last N days to compare against current VRP.
        
        Args:
            symbol (str): Symbol to analyze
            current_price (float): Current price
            exp_month (int): Expiration month
            exp_year (int): Expiration year
            days (int): Number of historical days to calculate (default: 20)
            is_index (bool): Whether symbol is an index
            
        Returns:
            Dict[str, Any]: Historical VRP data and statistics
        """
        try:
            # Get historical prices for a longer period to calculate VRP for each day
            historical_prices = self.get_historical_prices(symbol, days=days + 60, is_index=is_index)
            
            if not historical_prices or len(historical_prices) < days + 30:
                # Not enough data, return simplified analysis
                return {
                    'historical_vrp_30d': [],
                    'avg_vrp_30d': 0.0,
                    'current_vs_avg': 0.0,
                    'vrp_trend': 'insufficient_data',
                    'days_analyzed': 0
                }
            
            historical_vrp_30d = []
            
            # Calculate VRP for each of the last 'days' trading days
            for i in range(days):
                # Use prices up to day i from the end
                end_idx = len(historical_prices) - i
                prices_for_day = historical_prices[:end_idx]
                
                if len(prices_for_day) < 31:
                    continue
                
                # Calculate 30-day realized volatility for this day
                realized_vol = self.calculate_realized_volatility(prices_for_day, 30)
                
                if realized_vol is None:
                    continue
                
                # For historical VRP, we'll use current IV as proxy
                # In practice, you'd need historical options data
                # Note: This is a limitation - ideally we'd have historical IV data
                # Temporarily set silent mode for historical calculations
                original_silent = self.silent_mode
                self.silent_mode = True
                current_iv = self.get_comprehensive_implied_volatility(symbol, current_price, exp_month, exp_year)
                self.silent_mode = original_silent
                
                # Calculate VRP for this day
                vrp_30d = current_iv - realized_vol
                historical_vrp_30d.append(vrp_30d)
            
            if not historical_vrp_30d:
                return {
                    'historical_vrp_30d': [],
                    'avg_vrp_30d': 0.0,
                    'current_vs_avg': 0.0,
                    'vrp_trend': 'insufficient_data',
                    'days_analyzed': 0
                }
            
            # Calculate statistics
            avg_vrp_30d = sum(historical_vrp_30d) / len(historical_vrp_30d)
            current_vrp = historical_vrp_30d[0] if historical_vrp_30d else 0.0  # Most recent
            current_vs_avg = current_vrp - avg_vrp_30d
            
            # Determine trend
            if len(historical_vrp_30d) >= 5:
                recent_avg = sum(historical_vrp_30d[:5]) / 5  # Last 5 days
                older_avg = sum(historical_vrp_30d[5:10]) / 5 if len(historical_vrp_30d) >= 10 else recent_avg
                
                if recent_avg > older_avg + 0.01:
                    vrp_trend = 'increasing'
                elif recent_avg < older_avg - 0.01:
                    vrp_trend = 'decreasing'
                else:
                    vrp_trend = 'stable'
            else:
                vrp_trend = 'insufficient_data'
            
            return {
                'historical_vrp_30d': historical_vrp_30d,
                'avg_vrp_30d': avg_vrp_30d,
                'current_vs_avg': current_vs_avg,
                'vrp_trend': vrp_trend,
                'days_analyzed': len(historical_vrp_30d),
                'min_vrp': min(historical_vrp_30d),
                'max_vrp': max(historical_vrp_30d),
                'std_vrp': (sum((x - avg_vrp_30d) ** 2 for x in historical_vrp_30d) / len(historical_vrp_30d)) ** 0.5
            }
            
        except Exception as e:
            logging.error(f"Error calculating historical VRP for {symbol}: {e}")
            return {
                'historical_vrp_30d': [],
                'avg_vrp_30d': 0.0,
                'current_vs_avg': 0.0,
                'vrp_trend': 'error',
                'days_analyzed': 0
            }
    
    def generate_vrp_signal(self, vrp_30d: float, vrp_60d: float, 
                           vrp_percentile: float) -> Tuple[str, float, str]:
        """
        Generate trading signal based on VRP analysis.
        
        Args:
            vrp_30d (float): 30-day VRP
            vrp_60d (float): 60-day VRP
            vrp_percentile (float): VRP percentile ranking
            
        Returns:
            Tuple[str, float, str]: (signal_strength, confidence, recommended_strategy)
        """
        # Average the VRP measures
        avg_vrp = (vrp_30d + vrp_60d) / 2
        
        # Determine signal strength
        if avg_vrp >= 0.08 and vrp_percentile >= 80:
            signal = "Strong Sell"
            confidence = 0.9
            strategy = "Sell straddles/strangles, iron condors, covered calls"
        elif avg_vrp >= 0.04 and vrp_percentile >= 60:
            signal = "Sell"
            confidence = 0.7
            strategy = "Sell premium strategies, credit spreads"
        elif avg_vrp <= -0.08 and vrp_percentile <= 20:
            signal = "Strong Buy"
            confidence = 0.9
            strategy = "Buy straddles/strangles, long volatility plays"
        elif avg_vrp <= -0.04 and vrp_percentile <= 40:
            signal = "Buy"
            confidence = 0.7
            strategy = "Buy options, debit spreads, protective puts"
        else:
            signal = "Neutral"
            confidence = 0.5
            strategy = "Delta-neutral strategies, wait for better opportunities"
        
        return signal, confidence, strategy
    
    def analyze_vrp(self, symbol: str, current_price: float = None, 
                   exp_month: int = None, exp_year: int = None,
                   is_index: bool = True, silent: bool = False) -> VRPSignal:
        """
        Perform comprehensive VRP analysis.
        
        Args:
            symbol (str): Symbol to analyze
            current_price (float): Current price (will fetch if None)
            exp_month (int): Expiration month for IV calculation
            exp_year (int): Expiration year for IV calculation
            is_index (bool): Whether symbol is an index
            silent (bool): Whether to suppress console output (default: False)
            
        Returns:
            VRPSignal: Complete VRP analysis results
        """
        try:
            # Set silent mode for this analysis
            self.silent_mode = silent
            
            # Get current price if not provided - separate API call
            if current_price is None:
                if not silent:
                    print("📊 Fetching current price...")
                current_price = self.get_current_price_polygon(symbol, is_index)
            
            # Set default expiration if not provided (next month)
            if exp_month is None or exp_year is None:
                today = date.today()
                if exp_month is None:
                    exp_month = today.month + 1 if today.month < 12 else 1
                if exp_year is None:
                    exp_year = today.year if today.month < 12 else today.year + 1
            
            if not silent:
                print(f"🔍 Analyzing VRP for {symbol} @ ${current_price:.2f}")
            
            # Get historical prices - separate API call
            if not silent:
                print("📈 Fetching historical price data...")
            historical_prices = self.get_historical_prices(symbol, days=252, is_index=is_index)
            
            # Calculate realized volatilities
            if not silent:
                print("📊 Calculating realized volatilities...")
            
            if not historical_prices:
                if not silent:
                    print("   ❌ Cannot calculate VRP without historical price data")
                # Return error signal
                return VRPSignal(
                    symbol=symbol,
                    current_iv=0.0,
                    realized_vol_30d=0.0,
                    realized_vol_60d=0.0,
                    vrp_30d=0.0,
                    vrp_60d=0.0,
                    vrp_percentile=50.0,
                    signal_strength="Error",
                    confidence=0.0,
                    recommended_strategy="Cannot analyze - no historical price data available",
                    timestamp=datetime.now(),
                    historical_vrp_data=None
                )
            
            realized_vol_30d = self.calculate_realized_volatility(historical_prices, 30)
            realized_vol_60d = self.calculate_realized_volatility(historical_prices, 60)
            
            if realized_vol_30d is None or realized_vol_60d is None:
                if not silent:
                    print("   ❌ Insufficient historical data for volatility calculation")
                # Return error signal
                return VRPSignal(
                    symbol=symbol,
                    current_iv=0.0,
                    realized_vol_30d=0.0,
                    realized_vol_60d=0.0,
                    vrp_30d=0.0,
                    vrp_60d=0.0,
                    vrp_percentile=50.0,
                    signal_strength="Error",
                    confidence=0.0,
                    recommended_strategy="Cannot analyze - insufficient historical data",
                    timestamp=datetime.now(),
                    historical_vrp_data=None
                )
            
            # Get implied volatility
            if not silent:
                print("🎯 Calculating comprehensive implied volatility...")
            current_iv = self.get_comprehensive_implied_volatility(symbol, current_price, exp_month, exp_year)
            
            # Calculate VRP
            vrp_30d = current_iv - realized_vol_30d
            vrp_60d = current_iv - realized_vol_60d
            
            # Calculate percentile
            avg_vrp = (vrp_30d + vrp_60d) / 2
            vrp_percentile = self.calculate_vrp_percentile(avg_vrp, symbol)
            
            # Generate signal
            signal_strength, confidence, recommended_strategy = self.generate_vrp_signal(
                vrp_30d, vrp_60d, vrp_percentile
            )
            
            # Calculate historical VRP data
            historical_vrp_data = self.calculate_historical_vrp(symbol, current_price, exp_month, exp_year)
            
            # Create VRP signal object
            vrp_signal = VRPSignal(
                symbol=symbol,
                current_iv=current_iv,
                realized_vol_30d=realized_vol_30d,
                realized_vol_60d=realized_vol_60d,
                vrp_30d=vrp_30d,
                vrp_60d=vrp_60d,
                vrp_percentile=vrp_percentile,
                signal_strength=signal_strength,
                confidence=confidence,
                recommended_strategy=recommended_strategy,
                timestamp=datetime.now(),
                historical_vrp_data=historical_vrp_data
            )
            
            return vrp_signal
            
        except Exception as e:
            logging.error(f"Error in VRP analysis for {symbol}: {e}")
            # Return neutral signal on error
            return VRPSignal(
                symbol=symbol,
                current_iv=0.20,
                realized_vol_30d=0.20,
                realized_vol_60d=0.20,
                vrp_30d=0.00,
                vrp_60d=0.00,
                vrp_percentile=50.0,
                signal_strength="Neutral",
                confidence=0.0,
                recommended_strategy="Unable to analyze - check data availability",
                timestamp=datetime.now(),
                historical_vrp_data=None
            )
    
    def display_vrp_analysis(self, vrp_signal: VRPSignal):
        """Display VRP analysis results in a formatted table."""
        print(f"\n{'='*60}")
        print(f"🎯 VRP ANALYSIS: {vrp_signal.symbol}")
        print(f"{'='*60}")
        print(f"📅 Analysis Time: {vrp_signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"\n📊 VOLATILITY METRICS:")
        print(f"   Current IV:        {vrp_signal.current_iv:.1%}")
        print(f"   30-Day Realized:   {vrp_signal.realized_vol_30d:.1%}")
        print(f"   60-Day Realized:   {vrp_signal.realized_vol_60d:.1%}")
        
        print(f"\n🎯 VRP CALCULATIONS:")
        print(f"   30-Day VRP:        {vrp_signal.vrp_30d:+.1%}")
        print(f"   60-Day VRP:        {vrp_signal.vrp_60d:+.1%}")
        print(f"   VRP Percentile:    {vrp_signal.vrp_percentile:.0f}%")
        
        # Display historical VRP comparison if available
        if vrp_signal.historical_vrp_data and vrp_signal.historical_vrp_data['days_analyzed'] > 0:
            hist_data = vrp_signal.historical_vrp_data
            print(f"\n📈 HISTORICAL VRP COMPARISON ({hist_data['days_analyzed']} days):")
            print(f"   20-Day Avg VRP:    {hist_data['avg_vrp_30d']:+.1%}")
            print(f"   Current vs Avg:    {hist_data['current_vs_avg']:+.1%}")
            print(f"   VRP Range:         {hist_data['min_vrp']:+.1%} to {hist_data['max_vrp']:+.1%}")
            print(f"   VRP Trend:         {hist_data['vrp_trend'].replace('_', ' ').title()}")
            
            # Add trend interpretation
            if hist_data['current_vs_avg'] > 0.02:
                print(f"   📈 Current VRP is significantly above recent average")
            elif hist_data['current_vs_avg'] < -0.02:
                print(f"   📉 Current VRP is significantly below recent average")
            else:
                print(f"   ➡️  Current VRP is near recent average")
        
        # Color-code the signal
        signal_emoji = {
            "Strong Sell": "🔴",
            "Sell": "🟠", 
            "Neutral": "🟡",
            "Buy": "🟢",
            "Strong Buy": "🟢"
        }
        
        emoji = signal_emoji.get(vrp_signal.signal_strength, "⚪")
        
        print(f"\n{emoji} TRADING SIGNAL:")
        print(f"   Signal:            {vrp_signal.signal_strength}")
        print(f"   Confidence:        {vrp_signal.confidence:.0%}")
        print(f"   Strategy:          {vrp_signal.recommended_strategy}")
        
        # Add interpretation
        print(f"\n💡 INTERPRETATION:")
        avg_vrp = (vrp_signal.vrp_30d + vrp_signal.vrp_60d) / 2
        if avg_vrp > 0.05:
            print("   Options appear OVERPRICED - consider selling premium")
        elif avg_vrp < -0.05:
            print("   Options appear UNDERPRICED - consider buying options")
        else:
            print("   Options appear FAIRLY VALUED - neutral positioning")
        
        print(f"{'='*60}")
    
    def get_vrp_for_spread_analysis(self, symbol: str, current_price: float,
                                   exp_month: int, exp_year: int) -> Dict[str, Any]:
        """
        Get VRP data for integration with spread analysis.
        
        Returns:
            Dict[str, Any]: VRP data for spread analysis integration
        """
        vrp_signal = self.analyze_vrp(symbol, current_price, exp_month, exp_year, silent=True)
        
        return {
            'vrp_signal': vrp_signal,
            'current_iv': vrp_signal.current_iv,
            'realized_vol_30d': vrp_signal.realized_vol_30d,
            'vrp_30d': vrp_signal.vrp_30d,
            'vrp_percentile': vrp_signal.vrp_percentile,
            'signal_strength': vrp_signal.signal_strength,
            'confidence': vrp_signal.confidence,
            'recommended_strategy': vrp_signal.recommended_strategy,
            'is_options_expensive': vrp_signal.vrp_30d > 0.03,
            'is_options_cheap': vrp_signal.vrp_30d < -0.03,
            'historical_vrp_data': vrp_signal.historical_vrp_data
        } 